import React, { useState, useEffect } from 'react';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    FormControl,
    FormControlLabel,
    Checkbox,
    Select,
    MenuItem,
    InputLabel,
    Box,
    CircularProgress,
    IconButton,
    Divider
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { styled } from '@mui/system';
import { getAutoFillSourceQuestionsAPI, updateAutoFillSettingsAPI } from '../../../../../services/api/question.api';
import { QUESTION_INPUT_TYPE } from '../../../../../types/question.type';

const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(2, 3),
}));

const AutoFillSettingsDialog = ({
    open,
    onClose,
    question,
    onSave,
    latestType,
    transferType
}) => {
    // Auto-fill functionality states
    const [enableAutoFill, setEnableAutoFill] = useState(false);
    const [sourceTransitionType, setSourceTransitionType] = useState('');
    const [sourceQuestionId, setSourceQuestionId] = useState('');
    const [availableSourceQuestions, setAvailableSourceQuestions] = useState([]);
    const [loadingSourceQuestions, setLoadingSourceQuestions] =/*  */ useState(false);

    // Define transition type options for auto-fill
    const transitionTypeOptions = [
        { value: 'admission', label: 'Admission/readmission', forType: 'admission' },        
        { value: 'return', label: 'Return', forType: 'return' },
        { value: 'hospitalTransfer', label: 'Hospital Transfer', forType: 'transfer', forTransferType: 'hospitalTransfer' },
        { value: 'safeDischarge', label: 'Safe Discharge', forType: 'transfer', forTransferType: 'safeDischarge' },
        { value: 'SNF', label: 'SNF', forType: 'transfer', forTransferType: 'SNF' },
        { value: 'AMA', label: 'AMA', forType: 'transfer', forTransferType: 'AMA' },
        { value: 'deceased', label: 'Deceased', forType: 'transfer', forTransferType: 'deceased' },
    ];

    // Fetch source questions for auto-fill
    const fetchSourceQuestions = async (transitionType) => {
        if (!transitionType || !question?.customQuestionInputType) return;

        setLoadingSourceQuestions(true);
        try {
            const params = {
                facilityid: localStorage.getItem("facilityId"),
                sourceTransitionType: transitionType,
                targetQuestionType: question.customQuestionInputType,
                questionId: question?._id,
                isCustom: question?.isCustom,
                questionType: question?.type,                
            };

            const response = await getAutoFillSourceQuestionsAPI(params);
            setAvailableSourceQuestions(response || []);
        } catch (error) {
            console.error('Error fetching source questions:', error);
            setAvailableSourceQuestions([]);
        } finally {
            setLoadingSourceQuestions(false);
        }
    };

    // Handle transition type change
    const handleTransitionTypeChange = (value) => {
        setSourceTransitionType(value);
        setSourceQuestionId(''); // Reset selected question
        fetchSourceQuestions(value);
    };

    // Handle auto-fill toggle
    const handleAutoFillToggle = (checked) => {
        setEnableAutoFill(checked);
        if (!checked) {
            // Reset auto-fill related fields
            setSourceTransitionType('');
            setSourceQuestionId('');
            setAvailableSourceQuestions([]);
        }
    };

    // Initialize auto-fill states when editing an existing question
    useEffect(() => {
        if (question) {

            // Check if auto-fill is enabled - backend stores directly on question object
            const autoFillEnabled = question.autoFillEnabled || false;
            const sourceTransitionType = question.sourceTransitionType || '';
            const sourceQuestionId = question.sourceQuestionId || '';
            
            // Always set the states based on the question data
            setEnableAutoFill(autoFillEnabled);
            setSourceTransitionType(sourceTransitionType);
            setSourceQuestionId(sourceQuestionId);

            // If auto-fill is enabled and has transition type, fetch source questions
            if (autoFillEnabled && sourceTransitionType) {
                fetchSourceQuestions(sourceTransitionType);
            } else {
                setAvailableSourceQuestions([]);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [question]);

    const handleSave = async () => {
        try {
            const autoFillData = {
                autoFillEnabled: enableAutoFill,
                sourceTransitionType: enableAutoFill ? sourceTransitionType : '',
                sourceQuestionId: enableAutoFill ? sourceQuestionId : ''
            };

            console.log('Saving auto-fill settings:', autoFillData);

            // Call the new API endpoint
            await updateAutoFillSettingsAPI(question._id, autoFillData);

            console.log('Auto-fill settings saved successfully');

            // Call the onSave callback to refresh the questions list
            if (onSave) {
                onSave(autoFillData);
            }

            onClose();
        } catch (error) {
            console.error('Error saving auto-fill settings:', error);
            // You might want to show an error message to the user here
        }
    };

    const handleClose = () => {
        // Reset states to original values when closing without saving
        if (question) {
            const autoFillEnabled = question.autoFillEnabled || false;
            const sourceTransitionType = question.sourceTransitionType || '';
            const sourceQuestionId = question.sourceQuestionId || '';

            setEnableAutoFill(autoFillEnabled);
            setSourceTransitionType(sourceTransitionType);
            setSourceQuestionId(sourceQuestionId);

            // If there were original settings, refetch the source questions to restore the dropdown
            if (autoFillEnabled && sourceTransitionType) {
                fetchSourceQuestions(sourceTransitionType);
            } else {
                setAvailableSourceQuestions([]);
            }
        }
        onClose();
    };

    if (!question) return null;

    return (
        <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
            <StyledDialogTitle>
                Auto-Fill Settings for "{question.label}"
                <IconButton aria-label="close" onClick={handleClose}>
                    <CloseIcon />
                </IconButton>
            </StyledDialogTitle>
            <Divider />
            <DialogContent>
                <Box sx={{ mt: 2 }}>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={enableAutoFill}
                                onChange={(e) => handleAutoFillToggle(e.target.checked)}
                                color="primary"
                            />
                        }
                        label="Enable auto-fill from existing questions"
                    />
                    {enableAutoFill && (
                        <Box sx={{ mt: 2, pl: 4 }}>
                            <FormControl fullWidth margin="normal">
                                <InputLabel id="transition-type-label">Source Transition Type</InputLabel>
                                <Select
                                    labelId="transition-type-label"
                                    value={sourceTransitionType || ''}
                                    onChange={(e) => handleTransitionTypeChange(e.target.value)}
                                    label="Source Transition Type"
                                    sx={{
                                        '& .MuiInputLabel-root': {
                                            backgroundColor: 'white',
                                            padding: '0 4px'
                                        }
                                    }}
                                >
                                    <MenuItem value="">
                                        <em>Select a transition type</em>
                                    </MenuItem>
                                    {transitionTypeOptions.map((option) => (
                                        <MenuItem key={option.value} value={option.value}>
                                            {option.label}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>

                            {sourceTransitionType && (
                                <FormControl fullWidth margin="normal">
                                    <InputLabel id="source-question-label">Source Question</InputLabel>
                                    <Select
                                        labelId="source-question-label"
                                        value={sourceQuestionId || ''}
                                        onChange={(e) => setSourceQuestionId(e.target.value)}
                                        label="Source Question"
                                        disabled={loadingSourceQuestions}
                                        sx={{
                                            '& .MuiInputLabel-root': {
                                                backgroundColor: 'white',
                                                padding: '0 4px'
                                            }
                                        }}
                                    >
                                        {loadingSourceQuestions ? (
                                            <MenuItem value="">
                                                <CircularProgress size={20} sx={{ mr: 1 }} />
                                                Loading questions...
                                            </MenuItem>
                                        ) : availableSourceQuestions.length === 0 ? (
                                            <MenuItem value="">
                                                <em>No compatible questions found</em>
                                            </MenuItem>
                                        ) : (
                                            <MenuItem value="">
                                                <em>Select a source question</em>
                                            </MenuItem>
                                        )}
                                        {!loadingSourceQuestions && availableSourceQuestions.length > 0 &&
                                            availableSourceQuestions.map((sourceQuestion) => (
                                                <MenuItem
                                                    key={sourceQuestion._id}
                                                    value={sourceQuestion._id}
                                                    sx={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        gap: 1,
                                                        whiteSpace: 'normal'
                                                    }}
                                                >
                                                    <span style={{ flex: 1 }}>{sourceQuestion.label}</span>
                                                    {sourceQuestion.customQuestionInputType && (
                                                        <span style={{
                                                            color: '#666',
                                                            fontSize: '0.8em',
                                                            flexShrink: 0
                                                        }}>
                                                            ({sourceQuestion.customQuestionInputType})
                                                        </span>
                                                    )}
                                                </MenuItem>
                                            ))
                                        }
                                    </Select>
                                </FormControl>
                            )}

                            {sourceTransitionType && availableSourceQuestions.length === 0 && !loadingSourceQuestions && (
                                <Box sx={{ mt: 1, p: 1, backgroundColor: '#fff3cd', borderRadius: 1 }}>
                                    <span style={{ color: '#856404', fontSize: '0.9em' }}>
                                        ❌ No compatible questions found for auto-fill.
                                        {question.customQuestionInputType === QUESTION_INPUT_TYPE.NUMBER_RANGE &&
                                            ' Number questions can only pull from other number questions.'}
                                        {question.customQuestionInputType === QUESTION_INPUT_TYPE.DATE &&
                                            ' Date questions can only pull from other date questions.'}
                                    </span>
                                </Box>
                            )}
                        </Box>
                    )}
                </Box>
            </DialogContent>
            <DialogActions>
                <Button onClick={handleClose} color="secondary">
                    Cancel
                </Button>
                <Button
                    onClick={handleSave}
                    color="primary"
                    variant="contained"
                    disabled={enableAutoFill && (!sourceTransitionType || !sourceQuestionId)}
                >
                    Save Auto-Fill Settings
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default AutoFillSettingsDialog;
