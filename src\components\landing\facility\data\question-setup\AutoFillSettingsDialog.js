import React, { useState, useEffect } from 'react';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    FormControl,
    FormControlLabel,
    Checkbox,
    Select,
    MenuItem,
    InputLabel,
    Box,
    CircularProgress,
    IconButton,
    Divider
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { styled } from '@mui/system';
import { getAutoFillSourceQuestionsAPI } from '../../../../../services/api/question.api';
import { QUESTION_INPUT_TYPE } from '../../../../../types/question.type';

const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(2, 3),
}));

const AutoFillSettingsDialog = ({
    open,
    onClose,
    question,
    onSave,
    latestType,
    transferType
}) => {
    // Auto-fill functionality states
    const [enableAutoFill, setEnableAutoFill] = useState(false);
    const [sourceTransitionType, setSourceTransitionType] = useState('');
    const [sourceQuestionId, setSourceQuestionId] = useState('');
    const [availableSourceQuestions, setAvailableSourceQuestions] = useState([]);
    const [loadingSourceQuestions, setLoadingSourceQuestions] = useState(false);

    // Helper function to check if question type supports auto-fill
    const supportsAutoFill = (inputType) => {
        // No auto-fill for questions with fixed options (dropdown, yes/no, etc.)
        const fixedOptionTypes = [
            QUESTION_INPUT_TYPE.LIMITED_ANSWERS,
            QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS,
        ];
        return !fixedOptionTypes.includes(inputType);
    };

    // Fetch source questions for auto-fill
    const fetchSourceQuestions = async (transitionType) => {
        if (!transitionType || !question?.customQuestionInputType) return;

        setLoadingSourceQuestions(true);
        try {
            const response = await getAutoFillSourceQuestionsAPI({
                facilityId: localStorage.getItem("facilityId"),
                forType: transitionType,
                questionType: question.customQuestionInputType,
                excludeQuestionId: question._id
            });

            setAvailableSourceQuestions(response || []);
        } catch (error) {
            console.error('Error fetching source questions:', error);
            setAvailableSourceQuestions([]);
        } finally {
            setLoadingSourceQuestions(false);
        }
    };

    // Handle transition type change
    const handleTransitionTypeChange = (value) => {
        setSourceTransitionType(value);
        setSourceQuestionId(''); // Reset selected question
        fetchSourceQuestions(value);
    };

    // Handle auto-fill toggle
    const handleAutoFillToggle = (checked) => {
        setEnableAutoFill(checked);
        if (!checked) {
            // Reset auto-fill related fields
            setSourceTransitionType('');
            setSourceQuestionId('');
            setAvailableSourceQuestions([]);
        }
    };

    // Initialize auto-fill states when editing an existing question
    useEffect(() => {
        if (question && question.autoFillEnabled) {
            setEnableAutoFill(true);
            if (question.sourceTransitionType) {
                setSourceTransitionType(question.sourceTransitionType);
                fetchSourceQuestions(question.sourceTransitionType);
            }
            if (question.sourceQuestionId) {
                setSourceQuestionId(question.sourceQuestionId);
            }
        } else {
            // Reset states for new question
            setEnableAutoFill(false);
            setSourceTransitionType('');
            setSourceQuestionId('');
            setAvailableSourceQuestions([]);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [question]);

    const handleSave = () => {
        const autoFillData = {
            autoFillEnabled: enableAutoFill,
            sourceTransitionType: enableAutoFill ? sourceTransitionType : '',
            sourceQuestionId: enableAutoFill ? sourceQuestionId : ''
        };
        onSave(autoFillData);
        onClose();
    };

    const handleClose = () => {
        // Reset states when closing
        setEnableAutoFill(question?.autoFillEnabled || false);
        setSourceTransitionType(question?.sourceTransitionType || '');
        setSourceQuestionId(question?.sourceQuestionId || '');
        setAvailableSourceQuestions([]);
        onClose();
    };

    if (!question) return null;

    return (
        <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
            <StyledDialogTitle>
                Auto-Fill Settings for "{question.label}"
                <IconButton aria-label="close" onClick={handleClose}>
                    <CloseIcon />
                </IconButton>
            </StyledDialogTitle>
            <Divider />
            <DialogContent>
                {supportsAutoFill(question.customQuestionInputType) ? (
                    <Box sx={{ mt: 2 }}>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={enableAutoFill}
                                    onChange={(e) => handleAutoFillToggle(e.target.checked)}
                                    color="primary"
                                />
                            }
                            label="Enable auto-fill from existing questions"
                        />

                        {enableAutoFill && (
                            <Box sx={{ mt: 2, pl: 4 }}>
                                <FormControl fullWidth margin="normal">
                                    <InputLabel>Source Transition Type</InputLabel>
                                    <Select
                                        value={sourceTransitionType}
                                        onChange={(e) => handleTransitionTypeChange(e.target.value)}
                                        label="Source Transition Type"
                                    >
                                        <MenuItem value="admission">Admission</MenuItem>
                                        <MenuItem value="return">Return</MenuItem>
                                        <MenuItem value="transfer">Transfer</MenuItem>
                                    </Select>
                                </FormControl>

                                {sourceTransitionType && (
                                    <FormControl fullWidth margin="normal">
                                        <InputLabel>Source Question</InputLabel>
                                        <Select
                                            value={sourceQuestionId}
                                            onChange={(e) => setSourceQuestionId(e.target.value)}
                                            label="Source Question"
                                            disabled={loadingSourceQuestions}
                                        >
                                            {loadingSourceQuestions ? (
                                                <MenuItem disabled>
                                                    <CircularProgress size={20} sx={{ mr: 1 }} />
                                                    Loading questions...
                                                </MenuItem>
                                            ) : (
                                                availableSourceQuestions.map((sourceQuestion) => (
                                                    <MenuItem key={sourceQuestion._id} value={sourceQuestion._id}>
                                                        {sourceQuestion.label}
                                                    </MenuItem>
                                                ))
                                            )}
                                        </Select>
                                    </FormControl>
                                )}

                                {sourceTransitionType && availableSourceQuestions.length === 0 && !loadingSourceQuestions && (
                                    <Box sx={{ mt: 1, p: 1, backgroundColor: '#fff3cd', borderRadius: 1 }}>
                                        <span style={{ color: '#856404', fontSize: '0.9em' }}>
                                            ❌ No compatible questions found for auto-fill.
                                            {question.customQuestionInputType === QUESTION_INPUT_TYPE.NUMBER_RANGE &&
                                                ' Number questions can only pull from other number questions.'}
                                            {question.customQuestionInputType === QUESTION_INPUT_TYPE.DATE &&
                                                ' Date questions can only pull from other date questions.'}
                                        </span>
                                    </Box>
                                )}
                            </Box>
                        )}
                    </Box>
                ) : (
                    <Box sx={{ mt: 2, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                        <span style={{ color: '#666', fontSize: '0.9em' }}>
                            ❌ Auto-fill is not available for this question type.
                            Questions with fixed options (dropdown, yes/no, etc.) cannot use auto-fill functionality.
                        </span>
                    </Box>
                )}
            </DialogContent>
            <DialogActions>
                <Button onClick={handleClose} color="secondary">
                    Cancel
                </Button>
                <Button 
                    onClick={handleSave} 
                    color="primary" 
                    variant="contained"
                    disabled={enableAutoFill && (!sourceTransitionType || !sourceQuestionId)}
                >
                    Save Auto-Fill Settings
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default AutoFillSettingsDialog;
