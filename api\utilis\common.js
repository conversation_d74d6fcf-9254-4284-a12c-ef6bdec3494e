const { round } = require("lodash");
const moment = require("moment");
const { FILTER_TYPES, CHART_FILTER_DAY_OF, TOTAL_TYPE, percentageByEnum } = require("../../types/common.type");
const _ = require("lodash");
const { toDisplayTime, momentDateFormat, getDayNameFromDate, getShiftName } = require("./date-format");
const mongoose = require("mongoose");

;

const saveLogs = async (data, type, userData, description = null) => {
    const Log = mongoose.model("log");
    const log = new Log({
        data,
        type,
        description,
        ...userData
    });
    await log?.save();
}

const filterAsyncData = async (arr, callback) => {
    const fail = Symbol();
    return (
        await Promise.all(
            arr.map(async (item) => ((await callback(item)) ? item : fail))
        )
    ).filter((i) => i !== fail);
};

const projectionPerMonth = async (total, filter) => {
    let projections = 0;
    const projectionDays = 30;
    const { startDate, endDate } = filter;
    var a = moment.utc(startDate);
    var b = moment.utc(endDate);
    let days = b.diff(a, 'days') + 1;
    //days = days === 0 ? 1 : days;
    if (days <= projectionDays) {
        projections = (projectionDays * total) / days;
    } else {
        const months = days / projectionDays;
        projections = total / months;
    }
    return projections ? Math.round(projections) : 0;
}

const getPercentage = (total, selectedTotal) => {
    let percentage = 0;
    if (total !== 0 && selectedTotal !== 0) {
        percentage = (total / selectedTotal) * 100;
    }
    return round(percentage);
}

function chartFilterPermission(filter) {
    const { startDate, endDate } = filter;
    if (startDate && endDate) {
        var dateA = moment(endDate);
        var dateB = moment(startDate);
        var diffDuration = moment.duration(dateA.diff(dateB));
        if (diffDuration.years() && diffDuration.years() > 0) {
            return [
                FILTER_TYPES.MONTHLY,
                FILTER_TYPES.YEARLY,
            ];
        } else if (diffDuration.months() && diffDuration.months() > 0) {
            return [
                FILTER_TYPES.MONTHLY
            ];
        }
    }
    return []
}

async function chartGroupBy(data, duration) {
    const formatted = data.map((elem) => {
        return {
            x: moment(elem.x).startOf(duration).format("YYYY-MM-DD"),
            y: elem.y
        };
    });
    const dates = formatted.map((elem) => elem.x);
    const uniqueDates = dates.filter((x, index) => dates.indexOf(x) === index);
    const latestData = uniqueDates.map((x) => {
        const y = formatted
            .filter((elem) => elem.x === x)
            .reduce((y, elem) => y + elem.y, 0);

        if (duration === CHART_FILTER_DAY_OF.MONTH) {
            return {
                x: moment(x).format("MMMM"),
                y
            };
        } else if (duration === CHART_FILTER_DAY_OF.YEAR) {
            return { x: moment(x).format("YYYY"), y };
        } else if (duration === CHART_FILTER_DAY_OF.WEEK) {
            return {
                x: `(${moment(x).startOf("week").format("MM-DD")})-(${moment(x)
                    .endOf("week")
                    .format("MM-DD")})`,
                y
            };
        } else {
            return { x: moment(x).format("MM-DD-YYYY"), y };
        }
    });
    return latestData;
}

async function get30DaysFromDates(startDateA, endDateB) {
    let dateStart = moment(startDateA);
    let dateEnd = moment(endDateB);

    let betweenMonthsLatest = [];
    let interimEndDate = dateEnd.clone();
    let interimLatestSub = interimEndDate.clone().subtract(29, 'days');

    while (dateStart < interimEndDate) {
        if (betweenMonthsLatest.length == 0) {
            betweenMonthsLatest.push({ startDate: interimEndDate.subtract(29, "days").format('YYYY-MM-DD'), endDate: interimEndDate.format('YYYY-MM-DD') });
        } else {
            if (dateStart < interimLatestSub) {
                betweenMonthsLatest.push({ startDate: dateStart.format('YYYY-MM-DD'), endDate: interimEndDate.subtract(1, "days").format('YYYY-MM-DD') });
            } else {
                betweenMonthsLatest.push({ startDate: interimEndDate.subtract(29, "days").format('YYYY-MM-DD'), endDate: interimEndDate.subtract(1, "days").format('YYYY-MM-DD') });
            }
        }
        interimEndDate.subtract(29, 'days');
        interimLatestSub.subtract(29, 'days');
    }
    if (betweenMonthsLatest.length > 0) {
        return _.sortBy(betweenMonthsLatest, "startDate");
    }
    return betweenMonthsLatest;
}

async function getMonthsFromDates(startDateA, endDateB) {
    var dateStart = moment(startDateA);
    var dateEnd = moment(endDateB);
    var interim = dateStart.clone();
    var betweenMonths = [];
    while (dateEnd > interim || interim.format('M') === dateEnd.format('M')) {
        if (betweenMonths.length == 0) {
            betweenMonths.push({ startDate: dateStart.format('YYYY-MM-DD'), endDate: interim.endOf('month').format('YYYY-MM-DD') });
        } else if (interim.format('M') === dateEnd.format('M')) {
            betweenMonths.push({ startDate: interim.startOf('month').format('YYYY-MM-DD'), endDate: dateEnd.format('YYYY-MM-DD') });
        } else {
            betweenMonths.push({ startDate: interim.startOf('month').format('YYYY-MM-DD'), endDate: interim.endOf('month').format('YYYY-MM-DD') });
        }
        interim.add(1, 'month');
    }
    if (betweenMonths.length > 0) {
        return _.sortBy(betweenMonths, "startDate");
    }
    return betweenMonths;
}

async function getYearFromDates(startDateA, endDateB) {
    var dateStart = moment(startDateA);
    var dateEnd = moment(endDateB);
    var interim = dateStart.clone();
    var betweenYears = [];
    while (dateEnd > interim || interim.format('YYYY') === dateEnd.format('YYYY')) {
        if (betweenYears.length == 0) {
            betweenYears.push({ startDate: dateStart.format('YYYY-MM-DD'), endDate: interim.endOf('year').format('YYYY-MM-DD') });
        } else if (interim.format('YYYY') === dateEnd.format('YYYY')) {
            betweenYears.push({ startDate: interim.startOf('year').format('YYYY-MM-DD'), endDate: dateEnd.format('YYYY-MM-DD') });
        } else {
            betweenYears.push({ startDate: interim.startOf('year').format('YYYY-MM-DD'), endDate: interim.endOf('year').format('YYYY-MM-DD') });
        }
        interim.add(1, 'year');
    }
    if (betweenYears.length > 0) {
        return _.sortBy(betweenYears, "startDate");
    }
    return betweenYears;
}

const matchedArray = (array1, array2) => {
    return _.intersectionWith(array1, array2, _.isEqual);
}

const updateListTotalValue = async (listData, matchedIds, type = "total", totalPatients = 0, totalFilter) => {
    const { totalType } = totalFilter;
    if (listData && listData.length > 0) {
        const latestData = []
        await filterAsyncData(listData, (async (RItem) => {
            const intersection = matchedArray(matchedIds, RItem.ids);
            let obj = {
                [type]: intersection.length || 0,
                ids: intersection,
                percentage: await itemPercentage(intersection.length, totalPatients, "number"),
            };
            if (totalType === TOTAL_TYPE.MAIN) {
                obj.originalTotal = intersection.length || 0;
                obj.isTooltip = false;
            } else {
                obj.originalTotal = RItem?.originalTotal || 0;
                obj.isTooltip = true;
            }
            latestData.push(Object.assign({}, RItem, obj))
        }));
        return latestData;
    }
}


const filterListDataItems = async (dataGroupBy, type, totalPatient, totalFilter) => {
    const { originalData = [], totalType = null, isOtherDashboard = false } = totalFilter;

    let listGroup = [];
    if (dataGroupBy) {
        for await (const [key, value] of Object.entries(dataGroupBy)) {
            const valueArr = value[0];
            if (key && valueArr && valueArr[type]) {
                let object = Object();
                object._id = key;
                object.id = key;
                object.label = valueArr[type].label;
                object.name = valueArr[type].label;
                object.total = value.length;
                object.value = value.length;
                let original = originalData[key] ? originalData[key]?.length : 0;
                object.originalTotal = original;
                object.isTooltip = totalType && totalType === TOTAL_TYPE.MAIN ? false : true;
                object.percentage = await itemPercentage(value.length, totalPatient, "number");
                object.patientIds = isOtherDashboard ? value.map((item) => item.refPatientId) : value.map((item) => String(item._id));
                if (isOtherDashboard) {
                    const patientIds = value.map((item) => String(item._id));
                    const uniquePatientIds = [...new Set(patientIds || [])];
                    object.otherDashboardIds = uniquePatientIds;
                }
                listGroup.push(object);
            }
        }
        if (listGroup.length > 0) {
            listGroup = _.orderBy(listGroup, "total", "desc");
        }
    }
    return listGroup;
}

const itemPercentage = async (total, mainTotalVal, type, forComparison) => {
    let selectedTotal = mainTotalVal;

    let percentage = 0;

    if (forComparison && total === mainTotalVal) {
        percentage = 0;
    } else if (total !== 0 && selectedTotal !== 0) {
        percentage = (total / selectedTotal) * 100;
    }
    percentage = percentage ? percentage : 0;
    if (type && type === "number") {
        return percentage < 1 ? percentage.toFixed(1) : percentage.toFixed();
    }
    if (type && type === "percentage") {
        return `${percentage.toFixed()}%`;
    }
    return `(${percentage.toFixed()}%)`;
}

const getRelationLabel = (row, relation) => {
    if (row && relation && row[relation]) {
        return row[relation]?.label
    } else {
        return "N/A"
    }
}

const getQuestionAnswer = (question, row) => {
    const { accessor, type, validationType, multiple } = question
    if (type === "text") {
        return row[accessor] ? row[accessor] : null
    } else if (type === "toggle") {
        // eslint-disable-next-line eqeqeq
        return row[accessor] && row[accessor] != '' ? "Yes" : "No"
    } else if (type === "date") {
        return row[accessor] ? toDisplayTime(row[accessor], "MM/DD/YYYY") : "-"
    } else if (type === "time") {
        return row[accessor] ? row[accessor] : null
    } else if (type === "validation") {
        if (multiple) {
            return row[validationType].map(prodData => prodData.label).join(', ')
        } else {
            const labelAccessor = accessor === "payerSourceInsurance" && validationType === "insurance" ? "payerSourceInsurance" : validationType;
            return row[labelAccessor] ? row[labelAccessor]?.label : "N/A"
        }
    }
    return null
}

const questionTypes = [
    { type: "admission" },
    { type: "readmission" },
    { type: "return" },
    { type: "transfer", transferType: "hospitalTransfer", filterValue: "unplannedHospitalTransfer" },
    { type: "transfer", transferType: "safeDischarge" },
    { type: "transfer", transferType: "SNF" },
    { type: "transfer", transferType: "AMA" },
    { type: "transfer", transferType: "deceased" },
];
const questionTypesHospital = [
    { type: "return" },
    { type: "transfer", transferType: "hospitalTransfer", filterValue: "unplannedHospitalTransfer" },
];

// const getChartFacilityPercentageBy = async (response, percentageBy = null) => {
//     if (percentageBy === percentageByEnum.bedCapacity) {
//         return response?.bedByFacility || [];
//     } else if (percentageBy === percentageByEnum.censusAverage) {
//         return response?.censusByFacility || []
//     } else {
//         return response?.censusByFacility || []
//     }
// }

const getDynamicPercentageBy = async (dbData, percentageBy = null, req) => {
    let percentageNumber = dbData?.censusAverage;
    if (percentageBy) {
        if (percentageBy === percentageByEnum.bedCapacity) {
            percentageNumber = dbData?.bedCapacity
        } else if (percentageBy === percentageByEnum.censusAsOfNow) {
            percentageNumber = dbData?.censusAsOfNow
        } else if (percentageBy === percentageByEnum.censusAverage) {
            percentageNumber = dbData?.censusAverage
        } else {
            const PercentageAgainst = mongoose.model("percentageAgainst");
            const percentageAgainstData = await PercentageAgainst.find({
                accountId: req?.accountId,
                userId: req?.userId,
            });
            if (percentageAgainstData && percentageAgainstData.length > 0) {
                percentageNumber = _.find(percentageAgainstData, { label: percentageBy })?.customPercentage ?? 0;
            } else {
                percentageNumber = dbData?.censusAverage
            }
        }
    }
    return percentageNumber;
}


const patientDataOrderBy = (patientData, order, orderBy) => {
    return _.orderBy(patientData,
        [
            (item) => {
                // Handle primary sort
                if (orderBy === 'DOB' || orderBy === 'dateOfADT' || orderBy === 'dateOfLatestAdmission') {
                    return new Date(item[orderBy]); // Convert to Date object for comparison
                }
                return item[orderBy]; // For other fields
            },
            (item) => new Date(item?.dateOfADT) // Always sort by dateOfADT as secondary
        ],
        [order, 'asc'] // Primary order direction and secondary is always ascending
    )
}

/**
 * Formats a patient/hospital data object with common fields and date formatting.
 * @param {Object} item - The patient/hospital data object.
 * @param {Object} options - Additional options for formatting.
 * @param {string} [options.patientId] - Optional patient ID to use as refPatientId.
 * @param {boolean} [options.reHospitalization] - Optional reHospitalization flag.
 * @param {boolean} [options.wasReturned] - Optional wasReturned flag.
 * @returns {Promise<Object>} The formatted object.
 */
async function formatPatientData(item, {
    patientId = null,
    reHospitalization = null,
    wasReturned = null } = {}
) {

    return {
        ...item,
        id: item._id?.toString?.() || item.id?.toString?.() || null,
        ...(patientId && { refPatientId: patientId }),
        ...(reHospitalization != null && { reHospitalization }),
        ...(wasReturned != null && { wasReturned }),
        dateOfADTOriginal: item.dateOfADT,
        DOBOriginal: item.DOB,
        dateOfADT: item.dateOfADT ? await momentDateFormat(item.dateOfADT, "YYYY-MM-DD") : null,
        DOB: item.DOB ? await momentDateFormat(item.DOB, "YYYY-MM-DD") : null,
        day: item.dateOfADT ? await getDayNameFromDate(item.dateOfADT) : null,
        shiftName: item.transferTime ? await getShiftName(item.transferTime) : null,
        insuranceId: item.insurance?._id?.toString() || null,
        floorId: item.unit?._id?.toString() || null,
        nurseId: item.nurse?._id?.toString() || null,
        doctorId: item.doctor?._id?.toString() || null,
        hospitalId: item.hospital?._id?.toString() || null,
        hospitalIdStr: item.hospital?._id?.toString() || null,
        hospitalName: item.hospital?.label || null,
        dxIds: Array.isArray(item.dx) && item.dx.length > 0 ? item.dx.map((user) => user._id.toString()) : [],
        facility: item.facilityId,
        facilityId: item.facilityId?._id?.toString?.() || (typeof item.facilityId === 'string' ? item.facilityId : null),
        snfFacilityId: item.snf?._id?.toString() || null,
        assistantLivId: item.transferToWhichAssistedLiving?._id?.toString() || null
    };
}

const transitionTypeEnum =
{
    ADMISSION: "admission",
    READMISSION: "readmission",
    RETURN: "return",
    HOSPITAL_TRANSFER: "hospitalTransfer",
    SAFE_DISCHARGE: "safeDischarge",
    SNF: "SNF",
    AMA: "AMA",
    DECEASED: "deceased",
}

module.exports = {
    transitionTypeEnum,
    patientDataOrderBy,
    getDynamicPercentageBy,
    questionTypes,
    questionTypesHospital,
    getQuestionAnswer,
    getRelationLabel,
    itemPercentage,
    updateListTotalValue,
    filterListDataItems,
    matchedArray,
    get30DaysFromDates,
    filterAsyncData,
    projectionPerMonth,
    getPercentage,
    chartFilterPermission,
    chartGroupBy,
    getMonthsFromDates,
    getYearFromDates,
    saveLogs,
    formatPatientData
};
