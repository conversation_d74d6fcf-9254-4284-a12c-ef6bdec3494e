const mongoose = require("mongoose");
const { PAGE_TYPE, ADT_SUB_TYPES } = require("../../types/common.type");
const { questionTypes, transitionTypeEnum } = require("../utilis/common");
const { QUESTION_INPUT_TYPE } = require("../../types/question.type");
const QuestionOrder = mongoose.model("questionOrder");
const Question = mongoose.model("question");
const Account = mongoose.model("account");
const Patient = mongoose.model("patient");
const Facility = mongoose.model("facility");
const slugify = require('slugify');
const { omit, includes } = require("lodash");
const { checkADTDuplicate } = require("../utilis/date-format");
const Validation = mongoose.model("validation");


function slugToCamelCaseMain(slug) {
    if (!slug) {
        return '';
    }

    return slug
        .toLowerCase()
        .replace(/[-_]+(.)?/g, (_, c) => (c ? c.toUpperCase() : ''))
        .replace(/^./, (firstChar) => firstChar.toLowerCase());
}

async function createUniqueSlug(name, type = "validationBase") {
    const maxAttempts = 100;
    const baseSlug = slugify(name, { lower: true, strict: true });
    const baseCamelCase = slugToCamelCaseMain(baseSlug);

    let counter = 0;

    while (counter < maxAttempts) {
        const slugToCheck = counter > 0 ? `${baseCamelCase}${counter}` : baseCamelCase;

        // Double-check for uniqueness
        try {
            const exists = await Question.exists({ [type]: slugToCheck });

            if (!exists) {
                // Double verification to prevent race conditions
                const finalCheck = await Question.exists({ [type]: slugToCheck });

                if (!finalCheck) {
                    return slugToCheck; // Return verified unique camelCase slug
                }
            }
        } catch (error) {
            console.error("Error checking slug existence:", error);
            // Consider how to handle database errors. Retrying might be appropriate in some cases.
            // For now, we'll continue to the next attempt.
        }

        counter++;
    }
    throw new Error("Failed to generate a unique slug after maximum attempts.");
}

// Example usage:
async function createQuestionWithUniqueSlug(questionName, type) {
    try {
        const uniqueSlug = await createUniqueSlug(questionName, type);

        return uniqueSlug;
    } catch (error) {
        console.error('Error creating question:', error);
        throw error; // Re-throw the error for further handling
    }
}

const duplicateQuestionLabel = async (req) => {
    const { body } = req;
    const { accountid } = req.headers;

    if (!body?.forType || !body?.label) {
        throw new Error("Missing required fields: forType or label.");
    }

    const query = {
        forType: body.forType,
        ...(body?.forTransferType && body?.forTransferType?.length > 0 && { forTransferType: { $in: body.forTransferType } }),
        accountId: mongoose.Types.ObjectId(accountid),
        $or: [
            { label: { $regex: `^${body.label?.trim()}\\s*$`, $options: "i" } },
            { ...body.tableLabel && { tableLabel: { $regex: `^${body.tableLabel?.trim()}\\s*$`, $options: "i" } } }
        ]
    };

    // Exclude the current document when editing
    if (body?._id) {
        query._id = { $ne: body._id };
    }

    const existingQuestion = await Question.findOne(query);

    return existingQuestion || null;
};

const saveQuestion = async (req) => {
    const { body } = req;
    const { customData, isArchived = false, template } = body;
    delete body.customData;

    const question = new Question(body);

    if (question.accessor) {
        question.accessor = await createQuestionWithUniqueSlug(body?.label, "accessor");
    }

    if (customData && (customData.isCustom || customData?.customQuestionInputType)) {
        question.isCustom = true;
        let validationType = await createQuestionWithUniqueSlug(body?.label);
        question.validationBase = validationType;
        question.validationType = validationType;

        if (customData?.customQuestionInputType && customData?.customQuestionInputType === QUESTION_INPUT_TYPE.DATE) {
            question.type = "date";
            question.isRequired = true;
        }

        if (customData?.customQuestionInputType && (customData?.customQuestionInputType === QUESTION_INPUT_TYPE.TIME_TAB_RESIDENT_LIST || customData?.customQuestionInputType === QUESTION_INPUT_TYPE.TIME_TAB_RANGE)) {
            question.type = "time";
            question.isRequired = true;
        }
        question.timeRangeType = customData?.timeRangeType ?? null;
        question.customQuestionInputType = customData?.customQuestionInputType ?? null;
        question.customQuestionOptions = customData?.customQuestionOptions
            ? customData.customQuestionOptions.filter(option => option != null && option !== '')
            : [];
    }
    question.isArchived = isArchived;
    if (template) {
        question.template = template;
    }
    const newQuestion = await question.save();

    return newQuestion;
}

const getQuestionSetup = async (forType, forTransferType, accountid, facilityid, autoFilledData = null, req) => {
    const questions = await QuestionOrder.aggregate([
        {
            $match: {
                accountId: mongoose.Types.ObjectId(accountid),
                forType,
                forTransferType,
            },
        },
        { $unwind: "$order" },
        {
            $lookup: {
                from: "questions",
                localField: "order.questionId",
                foreignField: "_id",
                as: "question",
            },
        },
        { $unwind: "$question" },
        { $sort: { "order.order": 1 } },
    ]);
    if (autoFilledData) {
        if (questions.length > 0) {
            // Helper function to map source transition type to query parameters
            const getTransitionTypeMapping = (sourceTransitionType) => {
                const mapping = {
                    [transitionTypeEnum.ADMISSION]: { type: transitionTypeEnum.ADMISSION },
                    [transitionTypeEnum.READMISSION]: { type: transitionTypeEnum.READMISSION },
                    [transitionTypeEnum.RETURN]: { type: transitionTypeEnum.RETURN },
                    [transitionTypeEnum.HOSPITAL_TRANSFER]: {
                        type: "transfer",
                        transferType: [ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER, ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER]
                    },
                    [transitionTypeEnum.SAFE_DISCHARGE]: {
                        type: "transfer",
                        transferType: [ADT_SUB_TYPES.SAFE_DISCHARGE]
                    },
                    [transitionTypeEnum.SNF]: {
                        type: "transfer",
                        transferType: [ADT_SUB_TYPES.SNF]
                    },
                    [transitionTypeEnum.AMA]: {
                        type: "transfer",
                        transferType: [ADT_SUB_TYPES.AMA]
                    },
                    [transitionTypeEnum.DECEASED]: {
                        type: "transfer",
                        transferType: [ADT_SUB_TYPES.DECEASED]
                    }
                };
                return mapping[sourceTransitionType] || null;
            };

            // Process auto-fill for each question with proper async/await
            await Promise.all(questions.map(async (ele) => {
                try {
                    console.log('Processing question for auto-fill:', ele?.question?._id);

                    if (!ele?.question?.autoFillEnabled) {
                        return ele;
                    }

                    const { sourceTransitionType, sourceQuestionId, _id, customQuestionInputType, validationBase } = ele.question;

                    if (!sourceTransitionType || !sourceQuestionId) {
                        console.log('Missing auto-fill configuration for question:', ele?.question?._id);
                        return ele;
                    }
                    console.log(sourceTransitionType, 'sourceTransitionType');

                    // Get transition type mapping
                    const transitionMapping = getTransitionTypeMapping(sourceTransitionType);
                    if (!transitionMapping) {
                        console.log('Invalid source transition type:', sourceTransitionType);
                        return ele;
                    }

                    // Build query for finding source ADT record
                    console.log(autoFilledData, 'autoFilledData');
                    const duplicateCheck = await checkADTDuplicate(autoFilledData, true);
                    console.log(duplicateCheck, 'duplicateCheck');
                    const query = {
                        ...duplicateCheck,
                        facilityId: mongoose.Types.ObjectId(facilityid),
                        ...transitionMapping
                    };
                    console.log(query, 'query');

                    console.log('Searching for source ADT record with query:', query);

                    // Find the most recent ADT record matching the criteria
                    const adtResponse = await Patient.findOne(query)
                        .sort({ createdAt: -1 })
                        .limit(1)
                        .lean(); // Use lean() for better performance

                    if (!adtResponse) {
                        console.log('No matching ADT record found for auto-fill');
                        return ele;
                    }

                    // Find the source question to get its accessor
                    const sourceQuestion = await Question.findById(sourceQuestionId)
                        .select("_id accessor label validationBase customQuestionInputType")
                        .lean();

                    console.log(sourceQuestion, 'sourceQuestion');

                    if (!sourceQuestion || !sourceQuestion.accessor) {
                        console.log('Source question not found or missing accessor:', sourceQuestionId);
                        return ele;
                    }

                    // Extract the auto-fill value from the ADT record
                    let autoFillValue = adtResponse[sourceQuestion.accessor];

                    if (autoFillValue !== undefined && autoFillValue !== null) {
                        if (customQuestionInputType === QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS) {
                            const prevSelectedValue = await Validation.findOne({ _id: mongoose.Types.ObjectId(autoFillValue) }).lean();
                            console.log(prevSelectedValue, 'prevSelectedValue');
                            if (prevSelectedValue) {
                                const validation = await Validation.findOne({
                                    type: validationBase,
                                    isArchived: false,
                                    label: prevSelectedValue?.label
                                }).lean();
                                console.log(validation, 'validation');

                                if (!validation) {
                                    // Create new validation if not found
                                    const newValidation = new Validation({
                                        type: validationBase,
                                        label: prevSelectedValue?.label,
                                        isArchived: false,
                                        isEditable: true,
                                        active: true,
                                        createdBy: req.user?._id,
                                        facilityId: mongoose.Types.ObjectId(facilityid)
                                    });
                                    const newValidationRes = await newValidation.save();
                                    console.log(newValidationRes, 'newValidationRes');
                                    autoFillValue = newValidationRes?._id?.toString() || null;
                                    console.log('Created new validation:', newValidation, autoFillValue);
                                } else {
                                    autoFillValue = validation?._id?.toString() || null;
                                }
                            }
                            // console.log(validation, 'validation');
                            console.log(validationBase, 'validationBase');
                            console.log(autoFillValue, 'autoFillValue');


                        }
                        ele.question.autoFillValue = autoFillValue;
                        console.log(`Auto-filled value for question ${ele.question._id}:`, autoFillValue);
                    } else {
                        console.log('No value found in source record for accessor:', sourceQuestion.accessor);
                    }

                } catch (error) {
                    console.error('Error processing auto-fill for question:', ele?.question?._id, error);
                    // Continue processing other questions even if one fails
                }

                return ele;
            }));
        }
    }
    // console.log(questions, 'questions');    
    return questions;
};

const updateQuestionLabel = async (req) => {
    const { accountid } = req.headers;
    const questionId = req.query.id;
    const { forType, forTransferType = null, label, tableLabel, customData, isRequired, isArchived = false, templateType } = req.body;

    if (accountid && questionId && forType) {

        const questions = await Question.findById(questionId);

        if (customData && (customData.isCustom || customData?.customQuestionInputType) && questions) {
            questions.templateType = templateType;
            questions.isArchived = isArchived;
            questions.isCustom = true;
            if (customData?.timeRangeType) {
                questions.timeRangeType = customData?.timeRangeType;
            }
            questions.customQuestionInputType = customData?.customQuestionInputType ?? null;
            questions.customQuestionOptions = customData?.customQuestionOptions
                ? customData.customQuestionOptions.filter(option => {
                    if (typeof option === 'string') {
                        return option !== '';
                    } else if (typeof option === 'object' && option !== null) {
                        // Check if object is not empty. You might need more specific checks here.
                        return Object.keys(option).length > 0;
                    }
                    return false;
                })
                : [];

            if (customData?.customQuestionInputType) {
                questions.isRequired = customData?.customQuestionInputType !== QUESTION_INPUT_TYPE.RESIDENCE_LIST
            }
            await questions.save();
        }

        let q = {
            accountId: mongoose.Types.ObjectId(accountid),
            forType,
            ...forTransferType && { forTransferType },
        };

        const questionsOrder = await QuestionOrder.findOne(q);

        if (questionsOrder && questionsOrder.order && questionsOrder.order.length > 0) {
            const latestOrder = questionsOrder.order.map((ele) => {
                if (questionId === ele?.questionId?.toString()) {
                    return { ...ele._doc, label: label, tableLabel: tableLabel }
                } else {
                    return ele?._doc
                }
            });
            if (latestOrder) {
                await QuestionOrder.findByIdAndUpdate(
                    questionsOrder._id,
                    { order: latestOrder }
                );
            }
        }
    } else {
        console.log("not found");
    }
}

const getQuestionAsColumns = async ({ pageType, facilityid }, { accountid }) => {
    let forTransferType = [];

    let forType = [];
    if (pageType === PAGE_TYPE.HOSPITAL) {
        forType = ["transfer"]
        forTransferType = [ADT_SUB_TYPES.HOSPITAL_TRANSFER];
    } else if (pageType === PAGE_TYPE.COMMUNITY_TRANSFER) {
        forType = ["transfer"]
        forTransferType = [ADT_SUB_TYPES.SAFE_DISCHARGE, ADT_SUB_TYPES.SNF, ADT_SUB_TYPES.AMA];
    } else if (pageType === PAGE_TYPE.DECEASED) {
        forType = ["transfer"]
        forTransferType = [ADT_SUB_TYPES.DECEASED];
    } else if (pageType === PAGE_TYPE.ADMISSION) {
        forType = ["admission"];
    }
    const questions = await QuestionOrder.aggregate([
        {
            $match: {
                //facilityId: { $in: facilityData },
                accountId: mongoose.Types.ObjectId(accountid),
                ...forType.length > 0 && ({ forType: { $in: forType } }),
                ...forTransferType.length > 0 && ({ forTransferType: { $in: forTransferType } }),
            },
        },
        { $unwind: "$order" },
        {
            $lookup: {
                from: "questions",
                localField: "order.questionId",
                foreignField: "_id",
                as: "question",
            },
        },
        { $unwind: "$question" },
        { $sort: { "order.order": 1 } },
    ]);
    if (questions?.length > 0) {
        for (const ele of questions) {
            if (ele?.question?.customQuestionInputType === QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS) {
                ele.question.validationOptions = (await Validation.find({
                    type: ele.question?.validationBase,
                }).select("label _id type")) || [];
            }
        }
    }
    return questions;
};

const setQuestions = async (facilityId, forType, transferType, accountId) => {
    let q = {
        active: true,
        forType,
        forTransferType: transferType,
        defaultOrder: { $exists: true },
    };

    if (transferType) {
        q.forTransferType = transferType;
    }
    const questions = await Question.find(q);

    let localOrder = {
        facilityId: facilityId,
        accountId: accountId,
        forType: questions[0].forType,
        forTransferType: questions[0].forTransferType,
        order: [],
    };

    await questions.forEach(async (question, idx) => {
        localOrder.order.push({
            questionId: question._id,
            order: question.defaultOrder,
        });
    });

    await new QuestionOrder(localOrder).save();
};

const setQuestionsByAccount = async (forType, transferType, accountId, filterValue) => {

    let q = {
        active: true,
        forType,
        forTransferType: transferType,
        defaultOrder: { $exists: true },
    };

    if (transferType) {
        q.forTransferType = transferType;
    }

    // if (filterValue) {
    //     q.forTransferType = filterValue
    // }

    const questions = await Question.find(q);

    let localOrder = {
        accountId: accountId,
        forType: questions[0].forType,
        forTransferType: questions[0].forTransferType, //filterValue && filterValue === "unplannedHospitalTransfer" ? "hospitalTransfer" : questions[0].forTransferType,
        order: [],
    };

    await questions.forEach(async (question, idx) => {
        localOrder.order.push({
            questionId: question._id,
            order: question.defaultOrder,
        });
    });

    await new QuestionOrder(localOrder).save();
};

const updateQuestionType = async () => {
    // let q = {
    //     active: true,
    //     forType: "transfer",
    //     forTransferType: { $in: ["unplannedHospitalTransfer"] },
    //     //defaultOrder: { $exists: true },
    // };

    // const questions = await Question.find(q);

    // if (questions && questions.length > 0) {
    //     await questions.forEach(async (question, idx) => {
    //         let questionSave = await Question.findById(question._id);    
    //         questionSave.forTransferType = "hospitalTransfer"
    //         await questionSave.save();
    //     });

    //     console.log(questions.length, "");
    // }

    let q = {
        active: true,
        forType: "transfer",
        forTransferType: { $in: ["hospitalTransfer"] },
        label: "Hospital transfer type",
        defaultOrder: { $exists: true },
    };

    const question = await Question.findOne(q);
    if (question) {
        let qo = {
            // _id: mongoose.Types.ObjectId("6620a60c36311de076e48110"),
            //active: true,
            forType: "transfer",
            forTransferType: { $in: ["hospitalTransfer"] },
            //defaultOrder: { $exists: true },
        };

        const questionsOrders = await QuestionOrder.find(qo);
        if (questionsOrders && questionsOrders.length > 0) {
            let questionUpdate = {
                questionId: question._id,
                order: -1,
            }

            await questionsOrders.forEach(async (questionsOrder) => {
                let isUpdate = true;
                await questionsOrder.order.forEach(ele => {
                    if (question._id.toString() === ele?.questionId.toString()) {
                        isUpdate = false
                    }
                });

                if (isUpdate) {
                    await QuestionOrder.findByIdAndUpdate(
                        questionsOrder._id,
                        { $push: { order: questionUpdate } }
                    );
                }
            });
        }
    }
    console.log("--------------------------------Done------------------------------------------------");
}

const updateQuestionOrderManually = async ({ accountid = null }) => {
    const accounts = await Account.find();
    if (accounts.length > 0) {
        await accounts.forEach(async (account) => {
            const facilities = await Facility.find({ accountId: account?._id });
            if (facilities.length > 0) {
                await facilities.forEach(async (facility) => {
                    if (facility) {
                        await QuestionOrder.deleteMany({ facilityId: facility?.id });
                    }
                });
            }
            await questionTypes.forEach(async (question) => {
                await setQuestionsByAccount(question.type, question?.transferType, account?._id, question?.filterValue);
            });
        });
    }
    return accounts;

}

const getAccountQuestions = async (req) => {
    const { accountid } = req.headers;
    const account = await Account.findById(accountid);
    const { forType, forTransferType } = req.query;
    let q = {
        active: true,
        isCustom: { $exists: true },
        isArchived: true,
        forType,
        accountId: { $ne: account._id },
        // We will add $or outside
    };

    if (forTransferType && forTransferType.length > 0) {
        q.forTransferType = { $in: forTransferType };
    }

    // Now add the $or condition separately
    q.$or = [
        { questionRefId: { $exists: false } },
        { questionRefId: null }
    ];

    const questions = await Question.find(q);
    return questions;
}

const cloneAccountQuestions = async (req) => {

    const { accountid } = req.headers;

    // Fetch account and questions
    const [account, questions] = await Promise.all([
        Account.findById(accountid),
        Question.find({ _id: { $in: req.body.questionIds } }).lean()
    ]);

    const newQuestions = await Promise.all(
        questions.map(async (question) => {
            // Generate unique slugs
            const accessor = await createQuestionWithUniqueSlug(
                question.accessor,
                "accessor"
            );

            const validationType = await createQuestionWithUniqueSlug(question.validationBase);

            // Omit unneeded fields
            const cleanQuestion = omit(question, [
                "_id",
                "createdAt",
                "updatedAt",
                "__v",
                "accountId",
            ]);

            const newQuestionData = {
                ...cleanQuestion,
                accessor,
                validationType,
                validationBase: validationType,
                questionRefId: question._id,
                accountId: new mongoose.Types.ObjectId(accountid),
                archivedAt: null
            };

            // Save new question
            const newQuestion = await new Question(newQuestionData).save();

            // Prepare query for QuestionOrder
            const query = {
                accountId: mongoose.Types.ObjectId(accountid),
                forType: newQuestionData.forType,
            };

            if (!includes(["return", "admission"], newQuestionData.forType)) {
                query.forTransferType = newQuestionData.forTransferType;
            }

            const questionsOrder = await QuestionOrder.findOne(query);

            if (questionsOrder) {
                const questionUpdate = {
                    questionId: newQuestion._id,
                    order: questionsOrder.order?.length + 1 || 0,
                    label: newQuestionData.label,
                    tableLabel: newQuestionData.tableLabel,
                };

                await QuestionOrder.findByIdAndUpdate(
                    questionsOrder._id,
                    { $push: { order: questionUpdate } }
                );
            }

            return newQuestion;
        })
    );

    return newQuestions;
}

const removeArchivedQuestions = async (req) => {
    const { id } = req.params;

    if (!id) {
        return { status: 400, message: "Question ID is required" };
    }

    try {
        const questionRefCount = await Question.countDocuments({ questionRefId: mongoose.Types.ObjectId(id) });

        if (questionRefCount > 0) {
            const question = await Question.findOneAndUpdate(
                { _id: mongoose.Types.ObjectId(id), isArchived: true },
                { isArchived: false },
                { new: true }
            );

            if (!question) {
                return { status: 404, message: "Archived question not found or already active" };
            }

            await Question.updateMany(
                { questionRefId: question._id },
                { isArchived: false, questionRefId: null }
            );

            return { status: 200, message: question };
        } else {
            const question = await Question.findOneAndUpdate(
                { _id: mongoose.Types.ObjectId(id), isArchived: true },
                { isArchived: false },
                { new: true }
            );
            return { status: 200, message: question };
        }
    } catch (error) {
        console.error("Error restoring question:", error);
        return { status: 500, message: "Internal server error" };
    }
};

const deleteQuestion = async (req, res) => {
    try {
        const questionId = req.query.id;

        if (!questionId) {
            return res.status(400).send({ message: "Question ID is required" });
        }

        const question = await Question.findById(questionId);

        if (!question) {
            return res.status(404).send({ message: "Question not found" });
        }

        if (question.isArchived) {
            if (question.questionRefId) {
                // If archived and has a reference ID, delete only if editable
                await Question.deleteOne({ _id: questionId, isEditable: true });
            } else {
                const questionRefCount = await Question.countDocuments({ questionRefId: mongoose.Types.ObjectId(questionId) });
                // If archived and no reference ID, just update archivedAt
                await Question.findByIdAndUpdate(questionId, { archivedAt: new Date() });
                // if (questionRefCount > 0) {
                // } else {
                //     await Question.deleteOne({ _id: questionId, isEditable: true });
                // }
            }
        } else {
            // If not archived, delete only if editable
            await Question.deleteOne({ _id: questionId, isEditable: true });
        }

        res.send(question);
    } catch (error) {
        console.error("Error deleting question:", error);
        res.status(500).send({ message: "Internal server error", error });
    }
}

/**
 * Get auto-fill compatible source questions based on transition type
 * @param {Object} req - Express request object
 * @returns {Array} Array of compatible questions for auto-fill
 */
const getAutoFillSourceQuestions = async (req) => {
    const { facilityid, sourceTransitionType, targetQuestionType, questionId } = req.query;
    const { accountid } = req.headers;

    if (!facilityid || !sourceTransitionType) {
        throw new Error("Missing required parameters: facilityid and sourceTransitionType");
    }

    // Map transition types to forType and forTransferType
    const transitionTypeMapping = {
        'admission': { forType: 'admission' },
        'readmission': { forType: 'admission' }, // Readmissions use admission questions
        'return': { forType: 'return' },
        'hospitalTransfer': { forType: 'transfer', forTransferType: 'hospitalTransfer' },
        'safeDischarge': { forType: 'transfer', forTransferType: 'safeDischarge' },
        'SNF': { forType: 'transfer', forTransferType: 'SNF' },
        'AMA': { forType: 'transfer', forTransferType: 'AMA' },
        'deceased': { forType: 'transfer', forTransferType: 'deceased' },
    };

    const mapping = transitionTypeMapping[sourceTransitionType];
    if (!mapping) {
        throw new Error(`Invalid transition type: ${sourceTransitionType}`);
    }

    try {
        // Build aggregation pipeline similar to getQuestionSetup
        const matchStage = {
            accountId: mongoose.Types.ObjectId(accountid),
            forType: mapping.forType,
        };

        // Add forTransferType if it exists in mapping
        if (mapping.forTransferType) {
            matchStage.forTransferType = mapping.forTransferType;
        }

        console.log('QuestionOrder match stage:', matchStage);

        // Use aggregation pipeline to get questions through QuestionOrder (like getQuestionSetup)
        const questions = await QuestionOrder.aggregate([
            {
                $match: matchStage
            },
            { $unwind: "$order" },
            {
                $lookup: {
                    from: "questions",
                    localField: "order.questionId",
                    foreignField: "_id",
                    as: "question",
                },
            },
            { $unwind: "$question" },
            {
                $match: {
                    "question.active": true,
                    "question._id": { $ne: mongoose.Types.ObjectId(questionId) }, // Exclude current question
                    $or: [
                        { "question.isArchived": { $exists: false } },
                        { "question.isArchived": { $ne: true } }
                    ]
                }
            },
            { $sort: { "order.order": 1 } },
        ]);

        console.log(`Found ${questions.length} questions from QuestionOrder`);

        // Filter questions based on auto-fill compatibility
        const compatibleQuestions = questions.filter(item => {
            const question = item.question;

            // Define question types that don't support auto-fill (fixed options)
            const fixedOptionTypes = [
                QUESTION_INPUT_TYPE.LIMITED_ANSWERS,
                QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS
            ];

            // Exclude questions with fixed options from being auto-fill sources
            if (fixedOptionTypes.includes(question.customQuestionInputType)) {
                return false;
            }

            // If target question type is provided, filter for compatible types
            if (targetQuestionType) {
                const numberTypes = [QUESTION_INPUT_TYPE.NUMBER_RANGE, QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS];

                if (numberTypes.includes(targetQuestionType)) {
                    // Number questions can only pull from other number questions
                    return numberTypes.includes(question.customQuestionInputType);
                } else if (targetQuestionType === QUESTION_INPUT_TYPE.DATE) {
                    // Date questions can only pull from other date questions
                    return question.type === "date";
                } else if (targetQuestionType === QUESTION_INPUT_TYPE.TIME_TAB_RESIDENT_LIST || targetQuestionType === QUESTION_INPUT_TYPE.TIME_TAB_RANGE) {
                    // Time questions can only pull from other time questions
                    return question.type === "time";
                } else if (targetQuestionType === QUESTION_INPUT_TYPE.RESIDENCE_LIST) {
                    return question.type === "text" || question.type === "date" || question.type === "time";
                } else {
                    // For other types, allow same type or unlimited answers
                    return question.customQuestionInputType === targetQuestionType ||
                        question.customQuestionInputType === QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS;
                }
            }

            // If no target type specified, allow all non-fixed-option questions
            return true;
        });

        console.log(`Filtered to ${compatibleQuestions.length} compatible questions`);

        // Transform to the expected format and add metadata
        const questionsWithMetadata = compatibleQuestions.map(item => ({
            _id: item.question._id,
            label: item.order?.label || item.question.label,
            tableLabel: item.order?.tableLabel || item.question.tableLabel,
            customQuestionInputType: item.question.customQuestionInputType,
            forType: item.question.forType,
            forTransferType: item.question.forTransferType,
            type: item.question.type,
            accessor: item.question.accessor,
            transitionType: sourceTransitionType,
            questionTypeLabel: getQuestionTypeLabel(item.question.customQuestionInputType || item.question.type),
            isCompatible: true
        }));

        // Sort by label for better UX
        questionsWithMetadata.sort((a, b) => (a.label || '').localeCompare(b.label || ''));

        console.log('Returning questions with metadata:', questionsWithMetadata.length);
        return questionsWithMetadata;

    } catch (error) {
        console.error('Error fetching auto-fill source questions:', error);
        throw error;
    }
};

/**
 * Helper function to get human-readable question type label
 * @param {string} questionType - Question input type
 * @returns {string} Human-readable label
 */
const getQuestionTypeLabel = (questionType) => {
    const typeLabels = {
        [QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS]: 'Unlimited answers',
        [QUESTION_INPUT_TYPE.LIMITED_ANSWERS]: 'Limited Answers',
        [QUESTION_INPUT_TYPE.NUMBER_RANGE]: 'Number',
        [QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS]: 'Number range / limited answers',
        [QUESTION_INPUT_TYPE.RESIDENCE_LIST]: 'Residence list',
        [QUESTION_INPUT_TYPE.DATE]: 'Date',
        [QUESTION_INPUT_TYPE.TIME_TAB_RESIDENT_LIST]: 'Time Tab - Resident List Only',
        [QUESTION_INPUT_TYPE.TIME_TAB_RANGE]: 'Time Tab - Time Range',
    };

    return typeLabels[questionType] || 'Unknown';
};

const updateAutoFillSettings = async (req) => {
    const questionId = req.params.questionId;
    const { autoFillEnabled, sourceTransitionType, sourceQuestionId, targetQuestionId } = req.body;

    try {
        // Validate input
        if (!questionId) {
            throw new Error("Question ID is required");
        }

        // Prepare update data
        const updateData = {
            autoFillEnabled: Boolean(autoFillEnabled),
            sourceTransitionType: autoFillEnabled ? sourceTransitionType : null,
            sourceQuestionId: autoFillEnabled ? (sourceQuestionId ? mongoose.Types.ObjectId(sourceQuestionId) : null) : null
        };

        // Update the question
        const updatedQuestion = await Question.findByIdAndUpdate(
            questionId,
            updateData,
            { new: true, runValidators: true }
        );

        if (!updatedQuestion) {
            throw new Error("Question not found");
        }

        console.log(`Auto-fill settings updated for question ${questionId}:`, updateData);
        return updatedQuestion;

    } catch (error) {
        console.error("Error updating auto-fill settings:", error);
        throw error;
    }
};

module.exports = {
    saveQuestion,
    deleteQuestion,
    duplicateQuestionLabel,
    getQuestionSetup,
    setQuestions,
    getQuestionAsColumns,
    updateQuestionType,
    updateQuestionOrderManually,
    setQuestionsByAccount,
    updateQuestionLabel,
    getAccountQuestions,
    cloneAccountQuestions,
    removeArchivedQuestions,
    getAutoFillSourceQuestions,
    updateAutoFillSettings
};
