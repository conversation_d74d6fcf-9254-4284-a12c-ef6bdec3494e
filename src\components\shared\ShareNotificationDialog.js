import React, { useState } from 'react';
import {
    <PERSON><PERSON>,
    DialogTitle,
    <PERSON>alogContent,
    Typo<PERSON>,
    Button,
    Box,
    Avatar,
    Slide,
    Divider
} from '@mui/material';
import {
    Share as ShareIcon,
    Visibility as ViewIcon,
    BookmarkAdd as KeepIcon,
    Delete as DeleteIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useDispatch } from 'react-redux';
import { ADD_NOTIFICATION } from '../../store/types';
import { respondToShare, markNotificationRead } from '../../services/custom-tab-sharing.service';
import { setIsScrollHightLight } from '../../store/reducers/common.slice';
import { setAdmissionCards, setCommunityTransferCards, setDeceasedCards, setHospitalCards, setOverallCards } from '../../store/reducers/customCard.slice';
import { useSelector } from 'react-redux';
import { PAGE_TYPE } from '../../types/pages.type';
import TitleConflictDialog from './TitleConflictDialog';
import { AUTH_ROLES } from '../../types/auth.type';

const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
});

const StyledDialog = styled(Dialog)(({ theme }) => ({
    '& .MuiDialog-paper': {
        borderRadius: 16,
        boxShadow: '0 24px 38px 3px rgba(0,0,0,0.14), 0 9px 46px 8px rgba(0,0,0,0.12), 0 11px 15px -7px rgba(0,0,0,0.2)',
        background: '#ffffff',
        color: '#333333',
        minWidth: '500px',
        maxWidth: '600px',
    },
}));

const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
    background: '#fff',
    color: '#000',
    padding: theme.spacing(3),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    textAlign: 'center'
}));

const StyledDialogContent = styled(DialogContent)(({ theme }) => ({
    padding: theme.spacing(3),
    background: '#ffffff',
}));

const ActionButton = styled(Button)(({ theme }) => ({
    borderRadius: 12,
    padding: theme.spacing(1.5, 3),
    fontWeight: 'bold',
    textTransform: 'none',
    fontSize: '1rem',
    minWidth: '140px',
    margin: theme.spacing(0.5),
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&.view-button': {
        background: 'linear-gradient(45deg, #4caf50, #81c784)',
        color: 'white',
        boxShadow: '0 4px 15px rgba(76, 175, 80, 0.4)',
        '&:hover': {
            background: 'linear-gradient(45deg, #43a047, #66bb6a)',
            transform: 'translateY(-2px)',
            boxShadow: '0 6px 20px rgba(76, 175, 80, 0.6)',
        }
    },
    '&.keep-button': {
        background: 'linear-gradient(45deg, #2196f3, #64b5f6)',
        color: 'white',
        boxShadow: '0 4px 15px rgba(33, 150, 243, 0.4)',
        '&:hover': {
            background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
            transform: 'translateY(-2px)',
            boxShadow: '0 6px 20px rgba(33, 150, 243, 0.6)',
        }
    },
    '&.delete-button': {
        background: 'linear-gradient(45deg, #f44336, #ef5350)',
        color: 'white',
        boxShadow: '0 4px 15px rgba(244, 67, 54, 0.4)',
        '&:hover': {
            background: 'linear-gradient(45deg, #d32f2f, #e57373)',
            transform: 'translateY(-2px)',
            boxShadow: '0 6px 20px rgba(244, 67, 54, 0.6)',
        }
    }
}));

const ShareNotificationDialog = ({
    open,
    notifications = [],
    onClose,
    onResponse,
    onViewTab
}) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isResponding, setIsResponding] = useState(false);
    const [titleConflict, setTitleConflict] = useState(null);
    const [showTitleConflictDialog, setShowTitleConflictDialog] = useState(false);
    const dispatch = useDispatch();
    const { hospitalCards, communityTransferCards, admissionCards, deceasedCards, overallCards } = useSelector((state) => state.customCard);
    const authUser = useSelector((state) => state.auth);

    const currentNotification = notifications[currentIndex];

    const shareData = currentNotification?.customTabShareId;
    const senderName = currentNotification?.senderId ?
        `${currentNotification.senderId.fullName}` :
        'Someone';
    const tabTitle = currentNotification?.metadata?.tabTitle || 'a custom tab';

    // Get other recipients' full names, comma-separated
    const otherRecipients = currentNotification?.metadata?.otherRecipients || [];
    const otherRecipientNames = otherRecipients
        .map(r => r.fullName)
        .filter(Boolean)
        .join(', ');

    const handleResponse = async (response, newTitle = null) => {
        if (!shareData) return;

        setIsResponding(true);
        try {
            const result = await respondToShare(shareData, response, newTitle);
            // Handle title conflict
            if (result.status === 409 && result.data?.conflict) {
                setTitleConflict({
                    response: response,
                    originalTitle: result.data.originalTitle,
                    conflictingTitle: result.data.conflictingTitle,
                    shareId: result.data.shareId
                });
                setShowTitleConflictDialog(true);
                setIsResponding(false);
                return;
            }
            if (result.status === 200) {
                let message = '';
                // eslint-disable-next-line default-case
                switch (response) {
                    case 'accept':
                        message = `You accepted the shared tab "${newTitle || tabTitle}"`;
                        if (onViewTab && result.data?.customTab) {
                            await onViewTab(result.data.customTab);
                            if (onResponse) {
                                await onResponse(currentNotification._id, response, result.data);
                            }


                            if (result.data?.customTab?.page === PAGE_TYPE.HOSPITAL) {
                                dispatch(setHospitalCards([...hospitalCards, result.data.customTab?.slug]));
                            } else if (result.data?.customTab?.page === PAGE_TYPE.COMMUNITY_TRANSFER) {
                                dispatch(setCommunityTransferCards([...communityTransferCards, result.data.customTab?.slug]));
                            } else if (result.data?.customTab?.page === PAGE_TYPE.DECEASED) {
                                dispatch(setDeceasedCards([...deceasedCards, result.data.customTab?.slug]));
                            } else if (result.data?.customTab?.page === PAGE_TYPE.ADMISSION) {
                                dispatch(setAdmissionCards([...admissionCards, result.data.customTab?.slug]));
                            } else if (result.data?.customTab?.page === PAGE_TYPE.OVERALL) {
                                dispatch(setOverallCards([...overallCards, result.data.customTab?.slug]));
                            }
                            onClose();
                            setTimeout(() => {
                                const id = `${result.data.customTab.slug}_scroll`;

                                const element = document.getElementById(id);
                                if (element) {
                                    element.scrollIntoView({
                                        behavior: "auto",
                                        block: "center",
                                        // inline: "center",
                                    });
                                }
                                dispatch(setIsScrollHightLight(id));
                                setTimeout(() => {
                                    dispatch(setIsScrollHightLight(null));
                                }, 3800);
                            }, 1000);
                        }
                        break;
                    case 'keep':
                        message = `You saved the tab "${newTitle || tabTitle}" for later`;
                        break;
                    case 'delete':
                        message = `You declined the shared tab "${tabTitle}"`;
                        break;
                }

                dispatch({
                    type: ADD_NOTIFICATION,
                    payload: {
                        type: 'success',
                        label: message,
                        id: 'shareResponse',
                    },
                });

                // For 'keep' and 'delete' responses, handle notification and dialog state
                if (response !== 'accept') {
                    // Call the response callback
                    if (onResponse) {
                        await onResponse(currentNotification._id, response, result.data);
                    }
                    // Move to next notification or close if this was the last one
                    if (currentIndex < notifications.length - 1) {
                        setCurrentIndex(prev => prev + 1);
                    } else {
                        onClose();
                    }
                }
            }
        } catch (error) {
            if (error.response?.status === 409 && error.response?.data?.data?.conflict) {
                const errorData = error.response.data.data;
                setTitleConflict({
                    response: response,
                    originalTitle: errorData.originalTitle,
                    conflictingTitle: errorData.conflictingTitle,
                    shareId: errorData.shareId
                });
                setShowTitleConflictDialog(true);
                setIsResponding(false);
                return;
            } else {
                dispatch({
                    type: ADD_NOTIFICATION,
                    payload: {
                        type: 'error',
                        label: 'Failed to respond to share notification',
                        id: 'shareResponseError',
                    },
                });
            }
        } finally {
            setIsResponding(false);
        }
    };

    const handleTitleConflictConfirm = async (newTitle) => {
        if (titleConflict) {
            setShowTitleConflictDialog(false);
            await handleResponse(titleConflict.response, newTitle);
            setTitleConflict(null);
        }
    };

    const handleTitleConflictClose = () => {
        setShowTitleConflictDialog(false);
        setTitleConflict(null);
    };

    const getInitials = (name) => {
        return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase();
    };

    const isInformational = currentNotification && currentNotification.actionRequired === false;

    // Handler for informational notifications: Next
    const handleInformationalNext = async () => {
        const notificationId = currentNotification._id;
        try {
            await markNotificationRead(notificationId);
        } catch (e) {
            // Optionally handle error
        }
        if (currentIndex < notifications.length - 1) {
            setCurrentIndex(prev => prev + 1);
        } else {
            onClose();
        }
    };

    // Handler for informational notifications: Close
    const handleInformationalClose = async () => {
        const notificationId = currentNotification._id;
        try {
            await markNotificationRead(notificationId);
        } catch (e) {
            // Optionally handle error
        }
        onClose();
    };

    if (!currentNotification) return null;

    return (
        <>
            <StyledDialog
                open={open}
                TransitionComponent={Transition}
                keepMounted
                disableEscapeKeyDown
                maxWidth="md"
                fullWidth
            >
                <StyledDialogTitle>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <ShareIcon sx={{ fontSize: 32 }} />
                        <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
                            Custom Tab Shared {authUser?.role?.slug === AUTH_ROLES.SUPER && currentNotification?.metadata?.isAutoIncluded ? '' : 'With You'} 
                        </Typography>
                    </Box>
                </StyledDialogTitle>
                <Divider />
                <StyledDialogContent>
                    <Box sx={{ textAlign: 'center', mb: 3 }}>
                        <Avatar
                            sx={{
                                width: 80,
                                height: 80,
                                mx: 'auto',
                                mb: 2,
                                background: 'linear-gradient(45deg, #FF6B6B, #4ECDC4)',
                                fontSize: '2rem',
                                fontWeight: 'bold'
                            }}
                        >
                            {getInitials(senderName)}
                        </Avatar>

                        <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#333333', mb: 1 }}>
                            {senderName} {currentNotification.type === 'TAB_SHARED' ? 'shared' : currentNotification.type === 'TAB_UNSHARED' ? 'unshared' : currentNotification.type === 'TAB_DELETED' ? 'deleted' : 'updated'} a custom dashboard tab  {authUser?.role?.slug === AUTH_ROLES.SUPER && currentNotification?.metadata?.isAutoIncluded ? '' : 'with you'}.
                        </Typography>

                        <Typography variant="body1" sx={{ color: '#666666', mb: 2 }}>
                            Tab: <strong>"{tabTitle}"</strong>
                        </Typography>

                        {otherRecipientNames && (
                            <Typography variant="body2" sx={{ color: '#888', mb: 2 }}>
                                Also shared with: <strong>{otherRecipientNames}</strong>
                            </Typography>
                        )}

                        {notifications.length > 1 && (
                            <Typography variant="body2" sx={{ color: '#999999' }}>
                                {currentIndex + 1} of {notifications.length} notifications
                            </Typography>
                        )}
                    </Box>

                    <Divider sx={{ my: 3 }} />

                    {isInformational ? (
                        <>
                            <Typography variant="body1" sx={{ color: '#f44336', textAlign: 'center', mt: 2 }}>
                                {currentNotification.message || "This tab is no longer shared with you."}
                            </Typography>
                            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 4 }}>
                                {notifications.length > 1 && currentIndex < notifications.length - 1 && (
                                    <Button
                                        variant="contained"
                                        color="primary"
                                        onClick={handleInformationalNext}
                                        sx={{ borderRadius: 12, minWidth: 120, fontWeight: 'bold', textTransform: 'none' }}
                                    >
                                        Next
                                    </Button>
                                )}
                                {notifications.length > 0 && (
                                    <Button
                                        variant={currentIndex === notifications.length - 1 || notifications.length === 1 ? "contained" : "outlined"}
                                        color="primary"
                                        onClick={handleInformationalClose}
                                        sx={{ borderRadius: 12, minWidth: 120, fontWeight: 'bold', textTransform: 'none' }}
                                        style={{ display: (currentIndex === notifications.length - 1 || notifications.length === 1) ? 'block' : 'none' }}
                                    >
                                        Close
                                    </Button>
                                )}
                            </Box>
                        </>
                    ) : (
                        <>
                            <Typography variant="h6" sx={{ textAlign: 'center', mb: 3, fontWeight: 'bold' }}>
                                What would you like to do?
                            </Typography>

                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, alignItems: 'center' }}>
                                <ActionButton
                                    className="view-button"
                                    onClick={() => handleResponse('accept')}
                                    disabled={isResponding}
                                    startIcon={<ViewIcon />}
                                    size="large"
                                >
                                    View it now
                                </ActionButton>

                                <Typography variant="caption" sx={{ color: '#666666', textAlign: 'center' }}>
                                    Saves and opens the tab immediately
                                </Typography>

                                <ActionButton
                                    className="keep-button"
                                    onClick={() => handleResponse('keep')}
                                    disabled={isResponding}
                                    startIcon={<KeepIcon />}
                                    size="large"
                                >
                                    Keep it and view later
                                </ActionButton>

                                <Typography variant="caption" sx={{ color: '#666666', textAlign: 'center' }}>
                                    Saves the tab without opening it
                                </Typography>

                                <ActionButton
                                    className="delete-button"
                                    onClick={() => handleResponse('delete')}
                                    disabled={isResponding}
                                    startIcon={<DeleteIcon />}
                                    size="large"
                                >
                                    Delete it
                                </ActionButton>

                                <Typography variant="caption" sx={{ color: '#666666', textAlign: 'center' }}>
                                    Removes it from your view
                                </Typography>
                            </Box>
                        </>
                    )}
                </StyledDialogContent>
            </StyledDialog>

            <TitleConflictDialog
                open={showTitleConflictDialog}
                onClose={handleTitleConflictClose}
                onConfirm={handleTitleConflictConfirm}
                originalTitle={titleConflict?.originalTitle || ''}
                conflictingTitle={titleConflict?.conflictingTitle || ''}
                isLoading={isResponding}
            />
        </>
    );
};

export default ShareNotificationDialog; 