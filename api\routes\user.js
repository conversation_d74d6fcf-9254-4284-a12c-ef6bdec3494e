const mongoose = require("mongoose");
const route = require("express").Router();
const { attachRefreshToken, attachTenant } = require("../../utils/auth");
const { authenticate } = require("../middleware/authenticate");
const User = mongoose.model("user");
const Sentry = require("@sentry/node");
const _ = require("lodash");
const config = require("../../config");
const { deleteExternalUser, updateExternalUser } = require("../../tenant");

const axios = require('axios');
const jwt = require('jsonwebtoken');

const {
	createUser,
	loginUser,
	getUsersByAccount,
	updateUserPassword,
	getFacilityOptions,
	changeUserPassword,
	forgotPassword,
	updateNewPassword,
	updateUserProfile,
	syncUserReport,
	syncUserAlertReport,
	getUsersByAccountForNote,
	getAllUsersByAccount,
} = require("../helpers/user");
const authWithRole = require("../middleware/auth-with-role");
const { saveLogs } = require("../utilis/common");

// Create new user
route.post("/", authenticate, async (req, res) => {
	try {
		const userData = await User.findOne({ email: req.body.email });
		if (!userData) {
			const tenantId = req.cookies[config.tenantCookieName];
			let user = await createUser(req.body, tenantId);
			res.send(user);
		} else {
			res.status(400).send({
				message: "Email already exist",
			});
		}
	} catch (error) {
		res.status(500).send(error);
	}
});

route.post("/login", async (req, res) => {
	try {
		const { username, password } = req.body;
		const { accessToken, refreshToken, user } = await loginUser(username, password);

		attachRefreshToken(res, refreshToken);

		res.send({ token: accessToken, user });
	} catch (error) {
		Sentry.captureException(error);
		res.status(400).send({ message: error });
	}
});

// Get logged user
route.get("/", authenticate, async (req, res) => {
	try {
		let user = req.user;
		res.send({ user });
	} catch (error) {
		res.status(500).send(`Error getting user`);
	}
});

route.get("/facilities/options", authenticate, async (req, res) => {
	try {
		const facilityOptions = await getFacilityOptions(req);
		res.send(facilityOptions);
	} catch (error) {
		res.status(500).send(`Error getting user`);
	}
});

route.get("/:accountid", authWithRole("manageUsers"), async (req, res) => {
	try {
		const users = await getUsersByAccount(req.user, req);
		res.send(users);
	} catch (error) {
		res.status(500).send(`Error getting user`);
	}
});

route.get("/notes/:accountid", authWithRole("manageNote"), async (req, res) => {
	try {
		const users = await getUsersByAccountForNote(req.user, req);
		res.send(users);
	} catch (error) {
		res.status(500).send(`Error getting user`);
	}
});

route.post("/update-password", authenticate, async (req, res) => {
	try {
		if (req.user && req.user._id) {
			const userId = req.user._id;
			const user = await updateUserPassword(userId, { ...req.body, id: userId });
			res.send({ user });
		} else {
			res.status(400).send({ message: "Something went wrong, Please try again later." });
		}
	} catch (error) {
		res.status(400).send({ message: error.message });
	}
});

route.post("/new-password", async (req, res) => {
	try {
		const user = await updateNewPassword(req.body);
		res.send({ user });
	} catch (error) {
		res.status(400).send({ message: error.message });
	}
});

route.post("/change-password", authenticate, async (req, res) => {
	try {
		if (req.user && req.user._id) {
			const userId = req.user._id;
			const user = await changeUserPassword(userId, { ...req.body, id: userId });
			res.send({ user });
		} else {
			res.status(400).send({ message: "Something went wrong, Please try again later." });
		}
	} catch (error) {
		res.status(400).send({ message: error.message });
	}
});

route.post("/forgot-password", async (req, res) => {
	try {
		const user = await forgotPassword(req.body.email);
		res.send({ user });
	} catch (error) {
		res.status(400).send({ message: error.message });
	}
});

route.post("/update-profile", authenticate, async (req, res) => {
	try {
		if (req.user && req.user._id) {
			const userId = req.user._id;
			const response = await updateUserProfile(userId, req.body);
			res.status(200).send({ data: response });
		} else {
			res.status(400).send({ message: "Something went wrong, Please try again later." });
		}
	} catch (error) {
		res.status(400).send({ message: error.message });
	}
});

route.get("/all-users/:accountId", authWithRole("manageDashboard"), async (req, res) => {
	try {
		const users = await getAllUsersByAccount(req);
		res.send(users);
	} catch (error) {
		console.log(error);
		res.status(500).send(`Error getting user`);
	}
});

route.put("/:id", authenticate, async (req, res) => {
	try {
		const userId = req.params.id;
		let data = req.body;
		const userData = await User.findOne({ email: data.email, _id: { $ne: userId } });
		if (!userData) {
			const existingUser = await User.findById(userId);
			const tenantUserId = existingUser.tenantUserId;
			if (existingUser.email !== data.email && tenantUserId) {
				try {
					await updateExternalUser({ tenantUserId: tenantUserId, email: data.email });
				} catch (error) {
					return res.status(400).json({ message: 'Failed to update external user', error: error.message });
				}
			}
			// Check for newly added accounts before updating
			const oldAccountIds = existingUser.accounts
				.filter(account => account.access === true)
				.map(account => account.accountId.toString());

			const newAccountIds = data.accounts
				.filter(account => account.access === true)
				.map(account => account.accountId.toString());

			// Find accounts that were newly added and removed
			const addedAccountIds = newAccountIds.filter(accountId => !oldAccountIds.includes(accountId));
			const removedAccountIds = oldAccountIds.filter(accountId => !newAccountIds.includes(accountId));

			await User.findByIdAndUpdate(mongoose.Types.ObjectId(userId), data, { new: true });

			// If accounts were removed, remove custom tab shares for those accounts
			if (removedAccountIds.length > 0) {

				const CustomTabShare = require('../models/CustomTabShare');
				const CustomTabNotification = require('../models/CustomTabNotification');

				// Find and remove shares for removed accounts
				const sharesToRemove = await CustomTabShare.find({
					recipientId: userId,
					accountId: { $in: removedAccountIds.map(id => mongoose.Types.ObjectId(id)) },
					isActive: true
				});
				if (sharesToRemove.length > 0) {
					for (const share of sharesToRemove) {
						// Remove the share record
						await CustomTabShare.deleteOne({ _id: share._id });

						// Remove related notifications
						await CustomTabNotification.deleteMany({
							customTabShareId: share._id
						});

						// Also remove TAB_SHARED notifications for this user and account
						await CustomTabNotification.deleteMany({
							recipientId: userId,
							accountId: share.accountId,
							type: { $in: ['TAB_SHARED', 'TAB_UPDATED', 'TAB_UNSHARED'] }
						});
					}
				}
			}

			// If new accounts were added, share universal tabs for those accounts
			if (addedAccountIds.length > 0) {

				// Get updated user with role populated
				const updatedUser = await User.findById(userId).populate('role');
				if (updatedUser) {
					// Import the sharing function
					const { shareUniversalTabsWithNewUser } = require('../helpers/user');
					await shareUniversalTabsWithNewUser(updatedUser);
				}
			}

			await syncUserReport(userId);
			await syncUserAlertReport(userId);
			const userData = await User.findById(userId)
				.populate("role")
				.select(["-password", "-hashedKey"])
				.exec();
			res.status(200).send({ message: "User updated successfully.", data: userData });
		} else {
			res.status(400).send({
				message: "Email already exist",
			});
		}
	} catch (error) {
		res.send(error);
	}
});

route.delete("/:id", authWithRole("manageUsers"), async (req, res) => {
	try {
		const userId = req.params.id;

		const existingUser = await User.findById(userId);
		const tenantUserId = existingUser.tenantUserId;
		if (tenantUserId) {
			try {
				await deleteExternalUser({ tenantUserId: tenantUserId });
			} catch (error) {
				return res.status(400).json({ message: 'Failed to delete external user', error: error.message });
			}
		}

		const deleted = await User.findByIdAndDelete(mongoose.Types.ObjectId(userId)).select(["-password", "-hashedKey"]);

		await saveLogs(
			_.pick(deleted, ['_id', 'fullName', 'email', "facilities", "accountId", "role", "accounts"]),
			"userDelete",
			{
				accountId: req?.headers?.accountid,
				facilityId: req?.headers?.facilityid ? mongoose.Types.ObjectId(req?.headers?.facilityid) : null,
				userId
			},
			"User deleted successfully"
		);
		res.send(deleted);
	} catch (error) {
		res.send(error);
	}
});

route.get('/auth0/callback', async (req, res) => {
	const code = req.query.code;
	const state = req.query.state;

	try {
		let tenantId = null;
		if (state) {
			const decodedState = JSON.parse(atob(decodeURIComponent(state)));
			tenantId = decodedState.tenantId;
		}

		const tokenResponse = await axios.post(`https://${process.env.AUTH0_DOMAIN}/oauth/token`, {
			grant_type: 'authorization_code',
			client_id: process.env.AUTH0_CLIENT_ID,
			client_secret: process.env.AUTH0_CLIENT_SECRET,
			code,
			redirect_uri: `${process.env.REACT_APP_BASE_URL}/api/user/auth0/callback`,
		});

		const { id_token } = tokenResponse.data;
		const decodedToken = jwt.decode(id_token);
		let user = await User.findOne({ email: decodedToken.email });
		if (!user) {
			handleRedirectWithError(res);
		}

		const { accessToken, refreshToken } = await user.generateAuthTokens(user);

		attachRefreshToken(res, refreshToken);
		attachTenant(res, tenantId);

		res.redirect(`${process.env.APP_URL}/#/login?token=${accessToken}`);
	} catch (error) {
		console.error('Error during Auth0 callback:', error.response ? error.response.data : error.message);
		handleRedirectWithError(res);
	}
});

function handleRedirectWithError(res) {
	const errorName = 'AuthenticationFailed';
	const errorMessage = 'Authentication failed, please try again.';
	const encodedErrorMessage = encodeURIComponent(errorMessage);
	res.redirect(`${process.env.APP_URL}/#/login?error=${errorName}&message=${encodedErrorMessage}`);
}

module.exports = route;
