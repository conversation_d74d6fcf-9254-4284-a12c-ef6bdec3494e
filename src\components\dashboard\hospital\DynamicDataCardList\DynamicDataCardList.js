/* eslint-disable no-unused-vars */
import _ from "lodash";
import DynamicDataCardSkeleton from "./DynamicDataCardSkeleton";
import { QUESTIONS_TEMPLATE_TYPE } from "../../../../types/question.type";
import DxCardTemplate from "./template/DxCardTemplate";
import DayCardList from "../dayCardList/DayCardList";
import DoctorCard from "../../doctorCard/DoctorCard";
import FloorCardList from "../floorCardList/FloorCardList";
import NinetyDaysAnalysis from "../ninetyDaysAnalysis/NinetyDaysAnalysis";
import { HOSPITAL_CARDS_TYPE } from "../../../../types/hospital.type";
import ShiftCard from "../shiftCard/ShiftCard";
import InsuranceCard from "../../InsuranceCard/InsuranceCard";
import DischargeEmergencyList from "../dischargeEmergencyList/DischargeEmergencyList";
import HospitalizationsList from "../hospitalizationsList/HospitalizationsList";
import ReturnCardList from "../returnCardList/ReturnCardList";
import { CO_TRANSFER_CARDS_TYPE } from "../../../../types/community-transfer.type";
import SixtyDaysAnalysis from "../../community-transfer/sixtyDaysAnalysis/SixtyDaysAnalysis";
import DynamicDataCardTotalCard from "./DynamicDataCardTotalCard";
import { CUSTOM_TAB_TYPE } from "../../../../types/common.type";

const DynamicDataCardList = ({
	data,
	dataComparison,
	handleToggle,
	selectedItem = [],
	type,
	page,
	filter,
	filterComparison,
	averageCensusComparison,
	averageCensus,
	cardTitle,
	loading,
	projectionDays,
	priorityNumber,
	transferType,
	isComparingAgainstAvgCensus,
	lockedTotalBy,
	searchValue,
	question,
	spacialSelectedItem = [],
	filterListData,
	filterListDataComparison,
	totalTransfers,
	totalTransfersComparison,
	isCustomTab = false,
	filterTotal,
	handleCardClick,
	isSelectedTotalCard,
	cardFilter,
	isDisabled = false
}) => {
	console.log(data , 'data', type);
	
	if (loading) return <DynamicDataCardSkeleton />;
	const questionType = question?.type;
	const templateType = question?.templateType;

	const componentProps = {
		data,
		dataComparison,
		handleToggle,
		selectedItem,
		type,
		page,
		filter,
		filterComparison,
		averageCensusComparison,
		averageCensus,
		cardTitle,
		loading,
		projectionDays,
		priorityNumber,
		transferType,
		isComparingAgainstAvgCensus,
		lockedTotalBy,
		searchValue,
		question,
		spacialSelectedItem,
		filterListData,
		filterListDataComparison,
		totalTransfers,
		totalTransfersComparison,
		isCustom: true,
		comparisonData: dataComparison,
		isCustomTab,
		customTab: question,
		filterTotal,
		cardFilter,
		...templateType === CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE_ASS_LIV_DATA && { totalSafeDischarge: filterListData?.totalSafeDischarge ?? 0 }
	};

	if (questionType === CUSTOM_TAB_TYPE.combineTab) {
		return <DynamicDataCardTotalCard
			isDisabled={isDisabled}
			handleCardClick={handleCardClick}
			isSelectedTotalCard={isSelectedTotalCard}
			{...componentProps}
		/>;
	} else {
		switch (templateType) {
			case QUESTIONS_TEMPLATE_TYPE.DAY_CARD:
			case HOSPITAL_CARDS_TYPE.DAYS_DATA:
				return <DayCardList {...componentProps} />;

			case QUESTIONS_TEMPLATE_TYPE.USER_LIST:
			case HOSPITAL_CARDS_TYPE.DOCTOR_DATA:
			case HOSPITAL_CARDS_TYPE.NURSE_DATA:
				return <DoctorCard {...componentProps} />;

			case QUESTIONS_TEMPLATE_TYPE.FLOOR_CARD:
			case HOSPITAL_CARDS_TYPE.FLOORS_DATA:
				return <FloorCardList {...componentProps} />;

			case QUESTIONS_TEMPLATE_TYPE.ANALYSIS:
			case HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA:
				return (
					<NinetyDaysAnalysis
						{...componentProps}
						data={data?.length > 0 ? _.orderBy(data, "_id", "asc") : []}
						height="100%"
					/>
				);

			case CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA:
				return (
					<SixtyDaysAnalysis
						{...componentProps}
						data={data?.length > 0 ? _.orderBy(data, "_id", "asc") : []}
						height="100%"
					/>
				);

			case HOSPITAL_CARDS_TYPE.SHIFT_DATA:
				return <ShiftCard {...componentProps} />;

			case HOSPITAL_CARDS_TYPE.INSURANCE_DATA:
				return <InsuranceCard {...componentProps} />;

			case HOSPITAL_CARDS_TYPE.DCER_DATA:
				return <DischargeEmergencyList
					{...componentProps}
					cardTitle="DC / ER"
				/>;
			case HOSPITAL_CARDS_TYPE.HOSPITALIZATIONS:
				return <HospitalizationsList
					{...componentProps}
					cardTitle="New Hospitalizations &amp; Re-Hospitalizations"
				/>;

			case HOSPITAL_CARDS_TYPE.RETURNS_DATA:
				return <ReturnCardList
					{...componentProps}
					cardTitle="Returned / Didn't Return"
				/>;
			default:
				return <DxCardTemplate {...componentProps} />;
		}

	}

};

export default DynamicDataCardList;
