const mongoose = require("mongoose");
const { QUESTION_INPUT_TYPE, CUSTOM_TAB_TYPE } = require("../../types/common.type");
const CustomTab = mongoose.model("customTab");
const Question = mongoose.model("question");
const Validation = mongoose.model("validation");
const AlertReport = mongoose.model("alertReport");
const User = mongoose.model("user");
const slugify = require('slugify');
const { AUTH_ROLES } = require("../../types/auth.type");
const ReportsSubscriptions = mongoose.model("reportsSubscriptions");


function createSlug(name) {
    const slug = slugify(name, {
        lower: true, // Converts to lowercase
        strict: true, // Removes special characters
    });
    return `${slug}_customTab`;
}

function slugToCamelCaseMain(slug) {
    if (!slug) {
        return '';
    }

    return slug
        .toLowerCase()
        .replace(/[-_]+(.)?/g, (_, c) => (c ? c.toUpperCase() : ''))
        .replace(/^./, (firstChar) => firstChar.toLowerCase());
}

async function createUniqueSlug(name, type = "slug") {
    const maxAttempts = 100;
    const baseSlug = createSlug(name);
    let counter = 0;
    const baseCamelCase = slugToCamelCaseMain(baseSlug);

    while (counter < maxAttempts) {
        const slugToCheck = counter > 0 ? `${baseCamelCase}${counter}` : baseCamelCase;

        // Double-check for uniqueness
        try {
            const exists = await CustomTab.exists({ [type]: slugToCheck });

            if (!exists) {
                // Double verification to prevent race conditions
                const finalCheck = await CustomTab.exists({ [type]: slugToCheck });

                if (!finalCheck) {
                    return slugToCheck; // Return verified unique camelCase slug
                }
            }
        } catch (error) {
            console.error("Error checking slug existence:", error);
            // Consider how to handle database errors. Retrying might be appropriate in some cases.
            // For now, we'll continue to the next attempt.
        }
        counter++;
    }
    throw new Error("Failed to generate a unique slug after maximum attempts.");
}

// Create new custom tab
const createCustomTab = async (req) => {
    const user = req?.user;
    const { isEditMode, id } = req.body;
    const { accountid } = req.headers;
    const query = {
        accountId: mongoose.Types.ObjectId(accountid),
        userId: user._id,
        page: req.body.page,
    };

    // Use a case-insensitive regex for the title check
    if (req.body.title) {
        query.title = new RegExp(`^${req.body.title}$`, 'i'); // 'i' for case-insensitive
    }

    if (isEditMode && id) {
        // Check if user is the creator of the tab
        const existingTab = await CustomTab.findById(id);
        if (!existingTab) {
            return { status: 404, message: "Custom tab not found" };
        }

        if (existingTab.userId.toString() !== user._id.toString()) {
            return { status: 403, message: "You don't have permission to edit this tab" };
        }

        // Exclude the current document from duplicate check
        query._id = { $ne: mongoose.Types.ObjectId(id) };
    }

    // Check for duplicates
    const exists = await CustomTab.findOne(query);    

    if (exists) {
        return { status: 400, message: "Custom tab already exists" };
    }
    
    if (isEditMode && id) {
        const updateFields = {
            title: req.body.title,
            description: req.body.description,
            filters: req.body.filters,
            page: req.body.page,
        };

        const updatedTab = await CustomTab.findByIdAndUpdate(
            mongoose.Types.ObjectId(id),
            updateFields,
            { new: true } // returns the updated document
        );

        // Optional: check if update succeeded
        if (!updatedTab) {
            return { status: 404, message: "Custom tab not found" };
        }

        // Send notifications to users who have this tab shared with them
        try {
            const CustomTabShare = mongoose.model("customTabShare");
            const CustomTabNotification = mongoose.model("customTabNotification");

            // Find all active shares for this custom tab
            const activeShares = await CustomTabShare.find({
                customTabId: id,
                isActive: true,
                status: { $in: ['ACCEPTED', 'KEPT'] }
            }).populate('recipientId');

            // Create update notifications for all users who have access (except the updating user)
            if (activeShares.length > 0) {
                const notifications = [];
                for (const share of activeShares) {
                    // Don't send notification to the user who is performing the update
                    if (share.recipientId._id.toString() !== user._id.toString()) {
                        const notification = new CustomTabNotification({
                            customTabId: id,
                            customTabShareId: share._id,
                            recipientId: share.recipientId._id,
                            senderId: user._id,
                            accountId: accountid,
                            type: 'TAB_UPDATED',
                            title: 'Custom Dashboard Tab Updated',
                            message: `${user.fullName} has updated the custom dashboard tab "${updatedTab.title}" that is shared with you.`,
                            actionRequired: false,
                            metadata: {
                                tabTitle: updatedTab.title,
                                page: updatedTab.page
                            }
                        });
                        notifications.push(notification);
                    }
                }

                // Bulk insert notifications
                if (notifications.length > 0) {
                    await CustomTabNotification.insertMany(notifications);
                }
            }
        } catch (notificationError) {
            console.error("Error sending update notifications:", notificationError);
            // Don't fail the update if notifications fail
        }

        return { status: 200, data: updatedTab }
    } else {
        const slug = await createUniqueSlug(req.body.title, "slug");
        const filterCustomSave = new CustomTab({
            accountId: mongoose.Types.ObjectId(accountid),
            userId: user._id,
            slug,            
            ...req.body
        });
        const saved = await filterCustomSave.save()
        return { status: 200, data: saved }
    }
};

const getCustomTabs = async (req) => {
    const { accountid } = req.headers;
    const user = req?.user;

    const filters = await CustomTab.find({
        userId: user._id,
        accountId: accountid
    });

    return filters;
};

const processFiltersAsync = async (filters) => {
    if (!Array.isArray(filters)) return [];

    const accessorCountMap = {};

    const processedFilters = await Promise.all(
        filters.map(async (ele) => {
            if (!Array.isArray(ele.filters)) return ele;

            let accessor = "";
            let templateType = "";
            let dashboardType = "";

            // ✅ Correctly process ele.filters here
            ele.filters = await Promise.all(
                ele.filters.map(async (item) => {
                    if (Array.isArray(item.filters)) {
                        // Nested filters inside item.filters
                        item.filters = await Promise.all(
                            item.filters.map(async (nestedItem) => {
                                const isMainCard = nestedItem?.isMainCard ?? false
                                const { value } = nestedItem?.card ?? {};

                                if (isMainCard) {
                                    templateType = value;
                                    dashboardType = item?.dashboard;
                                }

                                let question = null;
                                let validationOptions = [];
                                if (nestedItem?.card?.questionId) {
                                    question = await Question.findById(nestedItem?.card?.questionId).lean();

                                    if (question?.customQuestionInputType === QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS) {
                                        validationOptions = await Validation.find({
                                            type: question.validationBase,
                                        }).select("label _id type").lean();
                                    }
                                }

                                return {
                                    ...nestedItem,
                                    question: question ? { ...question, validationOptions } : null,
                                };
                            })
                        );
                        return item;
                    } else {
                        // Normal single card (no nested filters)
                        const { value, isMainCard = false } = item?.card ?? {};

                        if (isMainCard) {
                            templateType = value;
                            dashboardType = item?.dashboard;
                        }

                        let question = null;
                        let validationOptions = [];

                        if (item.questionId) {
                            question = await Question.findById(item.questionId).lean();

                            if (question?.customQuestionInputType === QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS) {
                                validationOptions = await Validation.find({
                                    type: question.validationBase,
                                }).select("label _id type").lean();
                            }
                        }

                        return {
                            ...item,
                            question: question ? { ...question, validationOptions } : null,
                        };
                    }
                })
            );


            // 🔧 Handle duplicate accessor
            if (accessor) {
                if (accessorCountMap[accessor] != null) {
                    accessorCountMap[accessor] += 1;
                    accessor = `${accessor}_${accessorCountMap[accessor]}`;
                } else {
                    accessorCountMap[accessor] = 0;
                }
            }

            return {
                ...ele,
                label: ele?.title,
                accessor: ele?.slug ?? accessor,
                templateType,
                isCustomTab: true,
                dashboardType
            };
        })
    );
    
    return processedFilters;
};


const getCustomTabById = async (id) => {
    try {
        // Convert id to ObjectId
        const tabObjectId = mongoose.Types.ObjectId(id);

        // Fetch the specific custom tab by ID
        const tab = await CustomTab.findOne({ _id: tabObjectId }).lean();

        // If no tab is found, return null
        if (!tab) {
            return null;
        }

        // Process the tab through the same processing logic
        const [processedTab] = await processFiltersAsync([tab]);

        // Return the processed tab
        return processedTab;
    } catch (error) {
        console.error("Error in getCustomTabById:", error);
        throw error;
    }
};


const getCustomTabsByPage = async ({ accountid, page, userId }) => {
    try {
        const user = await User.findById(userId).populate("role").lean();
        let role = null;
        if (user) {
            role = user?.role?.slug;
        }
        
        const accountObjectId = mongoose.Types.ObjectId(accountid);
        const userObjectId = mongoose.Types.ObjectId(userId);

        let filters = [];

        // Get user's own custom tabs
        const ownTabs = await CustomTab.find({
            accountId: accountObjectId,
            page,
            userId: userObjectId
        }).lean();

        // Get shared tabs for this user with share details
        const CustomTabShare = mongoose.model("customTabShare");
        const sharedTabRecords = await CustomTabShare.find({
            recipientId: userObjectId,
            accountId: accountObjectId,
            isActive: true,
            status: { $in: ['ACCEPTED', 'KEPT'] }
        }).select('customTabId newTitle isHidden _id').lean();

        const sharedTabIds = sharedTabRecords.map(record => record.customTabId);

        const sharedTabs = await CustomTab.find({
            _id: { $in: sharedTabIds },
            page
        }).lean();

        // Create a map of customTabId to share details for quick lookup
        const shareDetailsMap = new Map();
        sharedTabRecords.forEach(record => {
            shareDetailsMap.set(record.customTabId.toString(), {
                newTitle: record.newTitle,
                isCustomShare: true,
                shareId: record._id,
                isHidden: record.isHidden || false
            });
        });

        // For superadmins, get all tabs from account
        // let adminTabs = [];
        // if (role === AUTH_ROLES.SUPER) {
        //     adminTabs = await CustomTab.find({
        //         accountId: accountObjectId,
        //         page,
        //         userId: { $ne: userObjectId }, // Exclude own tabs (already included)
        //         _id: { $nin: sharedTabIds } // Exclude already shared tabs
        //     }).lean();
        // }

        // Combine all tabs
        filters = [...ownTabs, ...sharedTabs];

        // If no filters are found, return an empty array
        if (!filters || filters.length === 0) {
            return [];
        }

        // Process the filters asynchronously
        const processedFilters = await processFiltersAsync(filters);

        // Add access information to each tab
        const enhancedFilters = processedFilters.map(tab => {
            let accessType = 'none';
            let canEdit = false;
            let isCustomShare = false;
            let resolvedTitle = tab.title;

            if (tab.userId && tab.userId.toString() === userId) {
                accessType = 'creator';
                canEdit = true;
            } else if (shareDetailsMap.has(tab._id.toString())) {
                accessType = 'shared';
                canEdit = false;

                // Get share details for this tab
                const shareDetails = shareDetailsMap.get(tab._id.toString());
                if (shareDetails) {
                    isCustomShare = shareDetails.isCustomShare;
                    // Use newTitle if available, otherwise use original title
                    if (shareDetails.newTitle) {
                        resolvedTitle = shareDetails.newTitle;
                    }
                }
            } else if (role === AUTH_ROLES.ADMIN || role === AUTH_ROLES.SUPER) {
                accessType = 'admin';
                canEdit = false;
            }

            // Get share details for additional metadata
            const shareDetails = shareDetailsMap.get(tab._id.toString());

            return {
                ...tab,
                title: resolvedTitle, // Use resolved title (newTitle if available)
                accessType,
                canEdit,
                isShared: accessType === 'shared',
                isCustomShare, // Flag indicating if this is a custom shared tab
                shareId: shareDetails?.shareId,
                isHidden: shareDetails?.isHidden || false
            };
        });

        return enhancedFilters;
    } catch (error) {
        console.error("Error in getCustomTabsByPage:", error);
        throw error;
    }
};


const getCustomTab = async (req) => {
    const { id } = req.query;
    const filters = await CustomTab.findOne({ _id: mongoose.Types.ObjectId(id) });
    return filters;
};

const deleteCustomTab = async (id, deletedByUserId = null) => {
    try {
        const customTab = await CustomTab.findById(id);
        if (!customTab) {
            console.warn(`CustomTab not found for id: ${id}`);
            return null;
        }

        // Get user info for notifications and role checking
        const deletedByUser = deletedByUserId ? await User.findById(deletedByUserId).populate('role') : null;
        const isCreator = customTab.userId.toString() === deletedByUserId?.toString();

        // Helper function to check if user has global delete permissions
        const hasGlobalDeletePermission = () => {
            if (!deletedByUser?.role?.slug) return false;
            const userRole = deletedByUser.role.slug;
            return userRole === AUTH_ROLES.SUPER || userRole === AUTH_ROLES.TOTAL;
        };

        // Determine if this should be a global deletion (for everyone)
        const shouldDeleteForEveryone = isCreator || hasGlobalDeletePermission();

        // Step 1: Handle sharing-related cleanup
        const CustomTabShare = mongoose.model("customTabShare");
        const CustomTabNotification = mongoose.model("customTabNotification");

        if (shouldDeleteForEveryone) {
            // Creator or privileged user is deleting - remove for everyone
            // Find all active shares for this tab
            const activeShares = await CustomTabShare.find({
                customTabId: id,
                isActive: true,
                status: { $in: ['PENDING', 'ACCEPTED', 'KEPT'] }
            }).populate('recipientId');

            // Create deletion notifications for all users who had access (except the deleting user)
            const notifications = [];

            // First, notify the creator if they're not the one deleting
            if (!isCreator && customTab.userId.toString() !== deletedByUserId.toString()) {
                const creatorNotification = new CustomTabNotification({
                    customTabId: id,
                    customTabShareId: null, // Creator doesn't have a share record
                    recipientId: customTab.userId,
                    senderId: deletedByUserId,
                    accountId: customTab.accountId,
                    type: 'TAB_DELETED',
                    title: 'Custom Tab Deleted',
                    message: `${deletedByUser.fullName} deleted your custom tab "${customTab.title}"`,
                    actionRequired: false,
                    metadata: {
                        tabTitle: customTab.title,
                        page: customTab.page,
                        deletedBy: `${deletedByUser.fullName}`
                    }
                });
                notifications.push(creatorNotification);
            }

            // Then, notify all users who have the tab shared with them
            for (const share of activeShares) {
                // Don't send notification to the user who is performing the deletion
                if (share.recipientId._id.toString() !== deletedByUserId.toString()) {
                    const notification = new CustomTabNotification({
                        customTabId: id,
                        customTabShareId: share._id,
                        recipientId: share.recipientId._id,
                        senderId: deletedByUserId,
                        accountId: customTab.accountId,
                        type: 'TAB_DELETED',
                        title: 'Custom Tab Deleted',
                        message: `${deletedByUser.fullName} deleted the custom tab "${customTab.title}"`,
                        actionRequired: false,
                        metadata: {
                            tabTitle: customTab.title,
                            page: customTab.page,
                            deletedBy: `${deletedByUser.fullName}`
                        }
                    });
                    notifications.push(notification);
                }

                // Deactivate the share
                share.status = 'DELETED';
                share.isActive = false;
                await share.save();
            }

            // Bulk insert notifications
            if (notifications.length > 0) {
                await CustomTabNotification.insertMany(notifications);
            }

            // Step 2: Remove matching customAlerts from AlertReports
            await AlertReport.updateMany(
                {
                    accountId: customTab.accountId,
                    page: customTab.page,
                    'customAlert.refSlug': customTab.slug
                },
                {
                    $pull: {
                        customAlert: { refSlug: customTab.slug }
                    }
                }
            );

            // Step 3: Delete related report subscriptions
            await ReportsSubscriptions.deleteMany({
                accountId: customTab.accountId,
                page: customTab.page,
                filterCardType: customTab.slug
            });

            // Step 4: Delete the custom tab            
            const deletedTab = await CustomTab.findByIdAndDelete(id);            
            if (!deletedTab) {
                console.warn(`Failed to delete CustomTab with id: ${id}`);
                return null;
            }

            return deletedTab;
        } else {
            // Regular user is removing the tab from their list
            const share = await CustomTabShare.findOne({
                customTabId: id,
                recipientId: deletedByUserId,
                isActive: true,
                status: { $in: ['ACCEPTED', 'KEPT'] }
            });

            if (share) {
                // Mark the share as deleted for this user only
                share.status = 'DELETED';
                share.isActive = false;
                await share.save();

                // Remove any customAlerts for this user
                await AlertReport.updateMany(
                    {
                        accountId: customTab.accountId,
                        page: customTab.page,
                        userId: deletedByUserId,
                        'customAlert.refSlug': customTab.slug
                    },
                    {
                        $pull: {
                            customAlert: { refSlug: customTab.slug }
                        }
                    }
                );

                // Remove any report subscriptions for this user
                await ReportsSubscriptions.deleteMany({
                    accountId: customTab.accountId,
                    page: customTab.page,
                    filterCardType: customTab.slug,
                    userId: deletedByUserId
                });

                return { status: 'success', message: 'Tab removed from your list' };
            }

            return null;
        }
    } catch (err) {
        console.log(err, 'err');
        
        console.error("Unexpected error during custom tab deletion:", err.message);
        return null;
    }
};

module.exports = {
    createCustomTab,
    getCustomTab,
    getCustomTabs,
    deleteCustomTab,
    getCustomTabsByPage,
    getCustomTabById,
    createUniqueSlug
};
