import React, { useState, useEffect } from 'react';
import {
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Typography,
    Box,
    Chip,
    Grid,
    Alert
} from '@mui/material';
import NumberRangeType from './NumberRangeType';

const DateAnalysisConfiguration = ({ setQuestionValues, questionValues, currentQuestions }) => {
    const [availableDateQuestions, setAvailableDateQuestions] = useState([]);
    const [selectedStartQuestion, setSelectedStartQuestion] = useState('');
    const [selectedEndQuestion, setSelectedEndQuestion] = useState('');

    // Filter date questions from currentQuestions
    useEffect(() => {
        if (currentQuestions && currentQuestions.length > 0) {
            const dateQuestions = currentQuestions.filter(item => {
                const question = item.question || item;
                return question.type === 'date' || question.customQuestionInputType === 'date';
            }).map(item => {
                const question = item.question || item;
                return {
                    accessor: question.accessor,
                    label: item.order?.label || question.label || question.tableLabel,
                    _id: question._id
                };
            });
            setAvailableDateQuestions(dateQuestions);
        }
    }, [currentQuestions]);

    // Initialize values from questionValues
    useEffect(() => {
        if (questionValues.dateAnalysisStartQuestion) {
            setSelectedStartQuestion(questionValues.dateAnalysisStartQuestion);
        }
        if (questionValues.dateAnalysisEndQuestion) {
            setSelectedEndQuestion(questionValues.dateAnalysisEndQuestion);
        }

        // Initialize customQuestionOptions if not already set
        if (!questionValues.customQuestionOptions || questionValues.customQuestionOptions.length === 0) {
            setQuestionValues(prevValues => ({
                ...prevValues,
                customQuestionOptions: []
            }));
        }
    }, [questionValues.dateAnalysisStartQuestion, questionValues.dateAnalysisEndQuestion, questionValues.customQuestionOptions, setQuestionValues]);

    const handleStartQuestionChange = (value) => {
        setSelectedStartQuestion(value);
        setQuestionValues(prevValues => ({
            ...prevValues,
            dateAnalysisStartQuestion: value
        }));
    };

    const handleEndQuestionChange = (value) => {
        setSelectedEndQuestion(value);
        setQuestionValues(prevValues => ({
            ...prevValues,
            dateAnalysisEndQuestion: value
        }));
    };



    // Get available end questions (exclude the selected start question)
    const getAvailableEndQuestions = () => {
        return availableDateQuestions.filter(q => q.accessor !== selectedStartQuestion);
    };

    return (
        <Box sx={{ mt: 2 }}>
            <Typography variant="h6" gutterBottom>
                Date Analysis Configuration
            </Typography>
            
            {availableDateQuestions.length === 0 && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                    No date questions found. Please create date questions first before setting up date analysis.
                </Alert>
            )}

            {availableDateQuestions.length > 0 && (
                <>
                    <Grid container spacing={2} sx={{ mb: 3 }}>
                        <Grid item xs={12} md={6}>
                            <FormControl fullWidth>
                                <InputLabel>Start Date Question</InputLabel>
                                <Select
                                    value={selectedStartQuestion}
                                    label="Start Date Question"
                                    onChange={(e) => handleStartQuestionChange(e.target.value)}
                                >
                                    {availableDateQuestions.map((question) => (
                                        <MenuItem key={question.accessor} value={question.accessor}>
                                            {question.label}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid item xs={12} md={6}>
                            <FormControl fullWidth>
                                <InputLabel>End Date Question</InputLabel>
                                <Select
                                    value={selectedEndQuestion}
                                    label="End Date Question"
                                    onChange={(e) => handleEndQuestionChange(e.target.value)}
                                    disabled={!selectedStartQuestion}
                                >
                                    {getAvailableEndQuestions().map((question) => (
                                        <MenuItem key={question.accessor} value={question.accessor}>
                                            {question.label}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>
                    </Grid>

                    {selectedStartQuestion && selectedEndQuestion && (
                        <Box sx={{ mb: 2 }}>
                            <Typography variant="subtitle1" gutterBottom>
                                Selected Date Range:
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                                <Chip 
                                    label={`Start: ${availableDateQuestions.find(q => q.accessor === selectedStartQuestion)?.label}`}
                                    color="primary"
                                    variant="outlined"
                                />
                                <Chip 
                                    label={`End: ${availableDateQuestions.find(q => q.accessor === selectedEndQuestion)?.label}`}
                                    color="primary"
                                    variant="outlined"
                                />
                            </Box>
                        </Box>
                    )}

                    <Box sx={{ mt: 3 }}>
                        <Typography variant="subtitle1" gutterBottom>
                            Analysis Options:
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            Configure the analysis types for this date range question (same as Number Range):
                        </Typography>

                        <NumberRangeType
                            setQuestionValues={setQuestionValues}
                            questionValues={questionValues}
                        />
                    </Box>
                </>
            )}
        </Box>
    );
};

export default DateAnalysisConfiguration;
