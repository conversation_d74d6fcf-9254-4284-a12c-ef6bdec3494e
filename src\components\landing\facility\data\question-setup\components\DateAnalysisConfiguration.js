import React, { useState, useEffect } from 'react';
import {
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Typography,
    Box,
    Chip,
    FormControlLabel,
    Checkbox,
    Grid,
    Alert
} from '@mui/material';
import { NUMBER_RANGE_TYPE } from '../../../../../../types/question.type';

const DateAnalysisConfiguration = ({ setQuestionValues, questionValues, currentQuestions }) => {
    const [availableDateQuestions, setAvailableDateQuestions] = useState([]);
    const [selectedStartQuestion, setSelectedStartQuestion] = useState('');
    const [selectedEndQuestion, setSelectedEndQuestion] = useState('');

    // Filter date questions from currentQuestions
    useEffect(() => {
        if (currentQuestions && currentQuestions.length > 0) {
            const dateQuestions = currentQuestions.filter(item => {
                const question = item.question || item;
                return question.type === 'date' || question.customQuestionInputType === 'date';
            }).map(item => {
                const question = item.question || item;
                return {
                    accessor: question.accessor,
                    label: item.order?.label || question.label || question.tableLabel,
                    _id: question._id
                };
            });
            setAvailableDateQuestions(dateQuestions);
        }
    }, [currentQuestions]);

    // Initialize values from questionValues
    useEffect(() => {
        if (questionValues.dateAnalysisStartQuestion) {
            setSelectedStartQuestion(questionValues.dateAnalysisStartQuestion);
        }
        if (questionValues.dateAnalysisEndQuestion) {
            setSelectedEndQuestion(questionValues.dateAnalysisEndQuestion);
        }
    }, [questionValues.dateAnalysisStartQuestion, questionValues.dateAnalysisEndQuestion]);

    const handleStartQuestionChange = (value) => {
        setSelectedStartQuestion(value);
        setQuestionValues(prevValues => ({
            ...prevValues,
            dateAnalysisStartQuestion: value
        }));
    };

    const handleEndQuestionChange = (value) => {
        setSelectedEndQuestion(value);
        setQuestionValues(prevValues => ({
            ...prevValues,
            dateAnalysisEndQuestion: value
        }));
    };

    const handleAnalysisOptionChange = (option, checked) => {
        const currentOptions = questionValues.customQuestionOptions || [];
        let newOptions;

        if (checked) {
            // Add option if not already present
            if (!currentOptions.some(opt => opt.type === option)) {
                newOptions = [...currentOptions, { type: option, option: getOptionLabel(option) }];
            } else {
                newOptions = currentOptions;
            }
        } else {
            // Remove option
            newOptions = currentOptions.filter(opt => opt.type !== option);
        }

        setQuestionValues(prevValues => ({
            ...prevValues,
            customQuestionOptions: newOptions
        }));
    };

    const getOptionLabel = (type) => {
        switch (type) {
            case NUMBER_RANGE_TYPE.RANGE:
                return 'Range';
            case NUMBER_RANGE_TYPE.AVERAGE:
                return 'Average';
            case NUMBER_RANGE_TYPE.TOTAL:
                return 'Total';
            default:
                return type;
        }
    };

    const isOptionSelected = (option) => {
        return questionValues.customQuestionOptions?.some(opt => opt.type === option) || false;
    };

    // Get available end questions (exclude the selected start question)
    const getAvailableEndQuestions = () => {
        return availableDateQuestions.filter(q => q.accessor !== selectedStartQuestion);
    };

    return (
        <Box sx={{ mt: 2 }}>
            <Typography variant="h6" gutterBottom>
                Date Analysis Configuration
            </Typography>
            
            {availableDateQuestions.length === 0 && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                    No date questions found. Please create date questions first before setting up date analysis.
                </Alert>
            )}

            {availableDateQuestions.length > 0 && (
                <>
                    <Grid container spacing={2} sx={{ mb: 3 }}>
                        <Grid item xs={12} md={6}>
                            <FormControl fullWidth>
                                <InputLabel>Start Date Question</InputLabel>
                                <Select
                                    value={selectedStartQuestion}
                                    label="Start Date Question"
                                    onChange={(e) => handleStartQuestionChange(e.target.value)}
                                >
                                    {availableDateQuestions.map((question) => (
                                        <MenuItem key={question.accessor} value={question.accessor}>
                                            {question.label}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid item xs={12} md={6}>
                            <FormControl fullWidth>
                                <InputLabel>End Date Question</InputLabel>
                                <Select
                                    value={selectedEndQuestion}
                                    label="End Date Question"
                                    onChange={(e) => handleEndQuestionChange(e.target.value)}
                                    disabled={!selectedStartQuestion}
                                >
                                    {getAvailableEndQuestions().map((question) => (
                                        <MenuItem key={question.accessor} value={question.accessor}>
                                            {question.label}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>
                    </Grid>

                    {selectedStartQuestion && selectedEndQuestion && (
                        <Box sx={{ mb: 2 }}>
                            <Typography variant="subtitle1" gutterBottom>
                                Selected Date Range:
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                                <Chip 
                                    label={`Start: ${availableDateQuestions.find(q => q.accessor === selectedStartQuestion)?.label}`}
                                    color="primary"
                                    variant="outlined"
                                />
                                <Chip 
                                    label={`End: ${availableDateQuestions.find(q => q.accessor === selectedEndQuestion)?.label}`}
                                    color="primary"
                                    variant="outlined"
                                />
                            </Box>
                        </Box>
                    )}

                    <Box sx={{ mt: 3 }}>
                        <Typography variant="subtitle1" gutterBottom>
                            Analysis Options:
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            Select the analysis types you want to include for this date range question:
                        </Typography>

                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={isOptionSelected(NUMBER_RANGE_TYPE.RANGE)}
                                        onChange={(e) => handleAnalysisOptionChange(NUMBER_RANGE_TYPE.RANGE, e.target.checked)}
                                    />
                                }
                                label="Range - Calculate the difference between start and end dates"
                            />
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={isOptionSelected(NUMBER_RANGE_TYPE.AVERAGE)}
                                        onChange={(e) => handleAnalysisOptionChange(NUMBER_RANGE_TYPE.AVERAGE, e.target.checked)}
                                    />
                                }
                                label="Average - Calculate average duration across multiple records"
                            />
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={isOptionSelected(NUMBER_RANGE_TYPE.TOTAL)}
                                        onChange={(e) => handleAnalysisOptionChange(NUMBER_RANGE_TYPE.TOTAL, e.target.checked)}
                                    />
                                }
                                label="Total - Sum up all durations"
                            />
                        </Box>

                        {questionValues.customQuestionOptions && questionValues.customQuestionOptions.length > 0 && (
                            <Box sx={{ mt: 2 }}>
                                <Typography variant="subtitle2" gutterBottom>
                                    Selected Analysis Options:
                                </Typography>
                                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                                    {questionValues.customQuestionOptions.map((option, index) => (
                                        <Chip
                                            key={index}
                                            label={option.option}
                                            color="secondary"
                                            size="small"
                                        />
                                    ))}
                                </Box>
                            </Box>
                        )}
                    </Box>
                </>
            )}
        </Box>
    );
};

export default DateAnalysisConfiguration;
