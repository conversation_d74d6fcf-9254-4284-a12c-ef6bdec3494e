const {
    shareCustomTab,
    respondToShare,
    unshareCustomTab,
    getSharedTabs,
    getTabShares,
    getNotifications,
    markNotificationRead
} = require("../helpers/custom-tab-sharing");
const authWithRole = require("../middleware/auth-with-role");
const mongoose = require("mongoose");
const CustomTabShare = mongoose.model("customTabShare");

const route = require("express").Router();

// Share a custom tab with users
route.post("/share", authWithRole("manageDashboard"), async (req, res) => {
    try {
        // Add request validation and debugging
        const { customTabId, recipientIds, shareMessage, isUniversal } = req.body;
        


        // Validate required fields
        if (!customTabId) {
            return res.status(400).send({ 
                status: 400, 
                message: "customTabId is required" 
            });
        }

        if (!recipientIds || !Array.isArray(recipientIds)) {
            return res.status(400).send({
                status: 400,
                message: "recipientIds must be an array"
            });
        }

        // Validate ObjectId format
        if (!mongoose.Types.ObjectId.isValid(customTabId)) {
            return res.status(400).send({ 
                status: 400, 
                message: "Invalid customTabId format" 
            });
        }

        if (recipientIds && recipientIds.length > 0) {
            for (const recipientId of recipientIds) {
                if (!mongoose.Types.ObjectId.isValid(recipientId)) {
                    return res.status(400).send({ 
                        status: 400, 
                        message: `Invalid recipient ID format: ${recipientId}` 
                    });
                }
            }
        }

        const result = await shareCustomTab(req);
        
        res.status(result.status || 200).send(result);
    } catch (error) {
        console.error("Share endpoint error:", error);
        res.status(500).send({ 
            status: 500, 
            message: error.message,
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});

// Respond to a share invitation (accept, keep, delete)
route.post("/share/respond", authWithRole("manageDashboard"), async (req, res) => {
    try {
        const { shareId, response, newTitle } = req.body;
        
        if (!shareId) {
            return res.status(400).send({
                status: 400,
                message: "shareId is required"
            });
        }

        if (!response) {
            return res.status(400).send({
                status: 400,
                message: "response is required"
            });
        }

        if (!['accept', 'keep', 'delete'].includes(response)) {
            return res.status(400).send({
                status: 400,
                message: "response must be 'accept', 'keep', or 'delete'"
            });
        }

        // Validate ObjectId format
        if (!mongoose.Types.ObjectId.isValid(shareId)) {
            return res.status(400).send({
                status: 400,
                message: "Invalid shareId format"
            });
        }

        // Validate newTitle if provided
        if (newTitle && typeof newTitle !== 'string') {
            return res.status(400).send({
                status: 400,
                message: "newTitle must be a string"
            });
        }

        const result = await respondToShare(req);
        res.status(result.status || 200).send(result);
    } catch (error) {
        console.error("Share response endpoint error:", error);
        res.status(500).send({
            status: 500,
            message: error.message,
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});

// Unshare a tab from specific users
route.post("/unshare", authWithRole("manageDashboard"), async (req, res) => {
    try {
        const result = await unshareCustomTab(req);
        res.status(result.status || 200).send(result);
    } catch (error) {
        res.status(500).send({ message: error.message });
    }
});

// Get tabs shared with current user
route.get("/shared-with-me", authWithRole("manageDashboard"), async (req, res) => {
    try {
        const result = await getSharedTabs(req);
        res.status(result.status || 200).send(result);
    } catch (error) {
        res.status(500).send({ message: error.message });
    }
});

// Get users who have access to a specific tab (for tab creators)
route.get("/:customTabId/shares", authWithRole("manageDashboard"), async (req, res) => {
    try {
        const result = await getTabShares(req);
        res.status(result.status || 200).send(result);
    } catch (error) {
        res.status(500).send({ message: error.message });
    }
});

// Get notifications for current user
route.get("/notifications", authWithRole("manageDashboard"), async (req, res) => {
    try {
        const result = await getNotifications(req);
        res.status(result.status || 200).send(result);
    } catch (error) {
        res.status(500).send({ message: error.message });
    }
});

// Mark notification as read
route.put("/notifications/:notificationId/read", authWithRole("manageDashboard"), async (req, res) => {
    try {
        const result = await markNotificationRead(req);
        res.status(result.status || 200).send(result);
    } catch (error) {
        res.status(500).send({ message: error.message });
    }
});

// Hide/Unhide shared tab
route.put("/shared/:shareId/toggle-visibility", authWithRole("manageDashboard"), async (req, res) => {
    try {
        const { shareId } = req.params;
        const { isHidden } = req.body;
        const userId = req.user._id;

        // Validate shareId
        if (!mongoose.Types.ObjectId.isValid(shareId)) {
            return res.status(400).send({
                status: 400,
                message: "Invalid shareId format"
            });
        }

        // Find and update the share record
        const shareRecord = await CustomTabShare.findOneAndUpdate(
            {
                _id: shareId,
                recipientId: userId,
                isActive: true,
                status: { $in: ['ACCEPTED', 'KEPT'] }
            },
            { isHidden: isHidden },
            { new: true }
        ).populate('customTabId', 'title');

        if (!shareRecord) {
            return res.status(404).send({
                status: 404,
                message: "Shared tab not found or you don't have permission"
            });
        }

        res.status(200).send({
            status: 200,
            message: isHidden ? 'Tab hidden successfully' : 'Tab unhidden successfully',
            data: shareRecord
        });

    } catch (error) {
        console.error("Error toggling tab visibility:", error);
        res.status(500).send({
            status: 500,
            message: error.message
        });
    }
});

module.exports = route;