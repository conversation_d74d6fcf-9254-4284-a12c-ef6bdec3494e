const { setCensus, updateCensus, firstCensusDate, getFirstAndLastDateOfInput, updateBedCapacity } = require("../helpers/census");
const authWithRole = require("../middleware/auth-with-role");

const router = require("express").Router();

router.post("/",  authWithRole("manageCensus"), async (req, res) => {
  try {    
    let census = await setCensus(req.body);
    if (!census) throw new Error("Census not created");
    res.send(census);
  } catch (error) {
    res.status(500).send(error);
  }
});

router.get("/first/date",  authWithRole("manageCensus"), async (req, res) => {
  try {
    let census = await firstCensusDate(req.query.facilityId);    
    if (!census) throw new Error("Census not created");
    res.send(census);
  } catch (error) {
    res.status(500).send(error);
  }
});

router.put("/:id",  authWithRole("manageCensus"), async (req, res) => {
  try {
    let census = await updateCensus(req.params.id, req);
    if (!census) throw new Error("Census not created");
    res.send(census);
  } catch (error) {
    res.status(500).send(error);
  }
});

router.post("/first-last-adt-date",  authWithRole("manageCensus"), async (req, res) => {
  try {
    let fistAndLastDated = await getFirstAndLastDateOfInput(req.body, req);
    res.send(fistAndLastDated);
  } catch (error) {
    res.status(500).send(error);
  }
});

router.post("/update-bed-capacity",  authWithRole("manageCensus"), async (req, res) => {
  try {
    let updatedData = await updateBedCapacity(req.body);
    res.send(updatedData);
  } catch (error) {
    res.status(500).send(error);
  }
});

module.exports = router;
