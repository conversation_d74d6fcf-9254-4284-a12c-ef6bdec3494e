const mongoose = require("mongoose");
const User = mongoose.model("user");
const Facility = mongoose.model("facility");
const _ = require("lodash");
const bcrypt = require("bcryptjs");
const { sendOnboardEmail, sendForgotPasswordEmail } = require("./email");
const { randomString } = require("../../utils/auth");
const ReportsSubscriptions = mongoose.model("reportsSubscriptions");
const AlertReport = mongoose.model("alertReport");
const { createExternalUser } = require('../../tenant');
const { AUTH_ROLES } = require("../../types/auth.type");
const CustomTab = mongoose.model("customTab");
const CustomTabShare = mongoose.model("customTabShare");
const CustomTabNotification = mongoose.model("customTabNotification");


// Function to get allowed creator roles based on new user's role
const getAllowedCreatorRoles = (userRole) => {
  // Returns which creator roles can share universal tabs with this user role
  switch (userRole) {
    case AUTH_ROLES.USER:
      // USER gets universal tabs from: ADMIN, REGIONAL, TOTAL, OWNER, SUPER
      // (admin universal affects any person, regional affects new admin+user, etc.)
      return [AUTH_ROLES.SUPER, AUTH_ROLES.OWNER, AUTH_ROLES.TOTAL, AUTH_ROLES.REGIONAL, AUTH_ROLES.ADMIN];
    case AUTH_ROLES.ADMIN:
      // ADMIN gets universal tabs from: REGIONAL, TOTAL, OWNER, SUPER
      // (regional affects new admin, total affects new regional+admin, etc.)
      return [AUTH_ROLES.SUPER, AUTH_ROLES.OWNER, AUTH_ROLES.TOTAL, AUTH_ROLES.REGIONAL];
    case AUTH_ROLES.REGIONAL:
      // REGIONAL gets universal tabs from: TOTAL, OWNER, SUPER
      // (total affects new regional, owner affects new total+regional+admin, etc.)
      return [AUTH_ROLES.SUPER, AUTH_ROLES.OWNER, AUTH_ROLES.TOTAL];
    case AUTH_ROLES.TOTAL:
      // TOTAL gets universal tabs from: OWNER, SUPER
      // (owner affects new total, super affects anybody)
      return [AUTH_ROLES.SUPER, AUTH_ROLES.OWNER];
    case AUTH_ROLES.OWNER:
      // OWNER gets universal tabs from: SUPER
      // (super affects anybody)
      return [AUTH_ROLES.SUPER];
    case AUTH_ROLES.SUPER:
      // SUPER gets universal tabs from: SUPER
      // (super affects anybody, including other supers)
      return [AUTH_ROLES.SUPER];
    default:
      return [];
  }
};

// Function to automatically share universal custom tabs with newly created user
const shareUniversalTabsWithNewUser = async (newUser) => {
  try {

    // Get user's role
    const userWithRole = await User.findById(newUser._id).populate('role');
    if (!userWithRole || !userWithRole.role) {
      console.log('User role not found, skipping universal tab sharing');
      return;
    }

    const userRole = userWithRole.role.slug;
    const allowedCreatorRoles = getAllowedCreatorRoles(userRole);

    if (allowedCreatorRoles.length === 0) {
      console.log(`No allowed creator roles for user role: ${userRole}`);
      return;
    }

    // Get all accounts where user has access
    const accessibleAccountIds = newUser.accounts
      .filter(account => account.access === true)
      .map(account => account.accountId);

    if (accessibleAccountIds.length === 0) {
      console.log('User has no accessible accounts, skipping universal tab sharing');
      return;
    }

    console.log(`User has access to ${accessibleAccountIds.length} accounts`);

    // Find all universal custom tabs in accessible accounts created by allowed roles
    const universalTabs = await CustomTab.find({
      accountId: { $in: accessibleAccountIds },
      isUniversal: true
    }).populate({
      path: 'userId',
      populate: {
        path: 'role',
        select: 'slug'
      }
    });
    // Filter tabs based on creator role hierarchy
    const eligibleTabs = universalTabs.filter(tab => {
      if (!tab.userId || !tab.userId.role) {
        return false;
      }
      const creatorRole = tab.userId.role.slug;
      return allowedCreatorRoles.includes(creatorRole);
    });

    if (eligibleTabs.length === 0) {
      return;
    }

    // Create share records and notifications for each eligible tab
    const shareRecords = [];
    // const notifications = [];

    for (const tab of eligibleTabs) {
      // Check if already shared (shouldn't happen for new user, but safety check)
      const existingShare = await CustomTabShare.findOne({
        customTabId: tab._id,
        recipientId: newUser._id,
        isActive: true
      });

      if (existingShare) {
        console.log(`Tab ${tab._id} already shared with user ${newUser._id}`);
        continue;
      }

      // Create share record
      const shareRecord = new CustomTabShare({
        customTabId: tab._id,
        creatorId: tab.userId._id,
        recipientId: newUser._id,
        accountId: tab.accountId,
        shareMessage: 'Automatically shared universal tab',
        status: 'ACCEPTED',
        // isAutoIncluded: true
      });

      shareRecords.push(shareRecord);
    }

    // Bulk save share records
    if (shareRecords.length > 0) {
      const savedShares = await CustomTabShare.insertMany(shareRecords);
      console.log(`Created ${savedShares.length} share records`);

     
      const tabIds = eligibleTabs.map(tab => tab._id);
      await CustomTab.updateMany(
        { _id: { $in: tabIds } },
        { $inc: { shareCount: 1 } }
      );
      console.log(`Updated share counts for ${tabIds.length} tabs`);
    }

    console.log(`Universal tab sharing completed for user: ${newUser._id}`);
  } catch (error) {
    console.error('Error sharing universal tabs with new user:', error);
    // Don't throw error to prevent user creation from failing
  }
};

const forgotPassword = async (email) => {
  let user = await User.findOne({ email });
  if (user) {
    const token = randomString(20);
    user.resetPasswordToken = token;
    await user.save();
    await sendForgotPasswordEmail(user, token);
  } else {
    throw new Error("Email Not found");
  }
};

const updateNewPassword = async (data) => {
  const { token } = data;
  if (token) {
    let user = await User.findOne({ resetPasswordToken: token });
    if (user) {
      user.password = data.password;
      user.requirePasswordReset = false;
      user.resetPasswordToken = null;
      return await user.save();
    } else {
      throw new Error("Token Expired, Please try again later.");
    }
  }
};

const createUser = async (data, tenantId) => {
  try {
    let inputValues = {};
    inputValues.password = Math.random()
      .toString(36)
      .substring(2, 12 + 2);
    inputValues.requirePasswordReset = true;
    inputValues.fullName = data.fullName ? data.fullName : null;
    inputValues.email = data.email ? data.email : null;
    inputValues.facilities = data.facilities ?? [];
    inputValues.role = data.role ? mongoose.Types.ObjectId(data.role) : null;
    inputValues.accounts = data.accounts ?? [];
    inputValues.jobTitle = data.jobTitle ?? null;

    if (tenantId) {
      try {
        const externalUser = await createExternalUser({ email: data.email.trim(), tenantId: tenantId });

        if (externalUser) {
          inputValues.tenantUserId = externalUser.id;
        }
      } catch (error) {
        throw new Error(`Failed to create external user: ${error.message}`);
      }
    }

    const localUser = new User(inputValues);

    let saved = await localUser.save().catch((e) => e);

    sendOnboardEmail(saved, inputValues.password);
    const latestUser = await User.findById(saved._id).populate("role");

    // Automatically share universal custom tabs with the new user
    await shareUniversalTabsWithNewUser(latestUser);

    return latestUser;
  } catch (err) {
    throw new Error(err);
  }
};

// Login user
const loginUser = async (username, password) => {
  let user = await User.findByCredentials(username, password);
  const { accessToken, refreshToken } = await user.generateAuthTokens(user);
  if (user) {
    user.password = undefined;
    user.hashedKey = undefined;
    user.resetPasswordToken = undefined;
  }
  return { accessToken, refreshToken, user };
};

const getUsersByAccountForNote = async (user, req) => {
  const accountId = req.params.accountid;
  let accountIdFilter;
  const roleName = user.role?.slug || null;
  accountIdFilter = { $elemMatch: { accountId: { $in: [accountId] } } };

  let users = [];

  users = await User.find({ accounts: accountIdFilter, _id: { $ne: user._id } })
    // .populate("role", null, { slug: { $in: ["user"] } })
    .select(["-password", "-hashedKey"])
    .exec();
  return users;
}

const getUsersByAccount = async (user, req) => {
  const { facilityId } = req.query;
  const accountId = req.params.accountid;
  let accountIdFilter;
  let slugs = [];
  const roleName = user.role?.slug || null;
  accountIdFilter = { $elemMatch: { accountId: { $in: [accountId] } } };
  if (user && user.role) {
    if (roleName == "super") {
      slugs = ["owner", "total", "regional", "admin", "user"];
    }
    if (roleName == "owner") {
      slugs = ["total", "regional", "admin", "user"];
    }
    if (roleName == "total") {
      slugs = ["regional", "admin", "user"];
    }
    if (roleName == "regional") {
      slugs = ["admin", "user"];
    }
    if (roleName == "admin") {
      slugs = ["user"];
    }
  }

  let users = [];

  if (facilityId) {
    users = await User.find({
      accounts: accountIdFilter,
      facilities: {
        $elemMatch: {
          facilityId: facilityId,
          $or: [
            {
              access: true,
            },
            {
              write: true,
            },
            {
              read: true,
            },
          ],
        },
      },
    })
      .populate("role", null, { slug: { $in: slugs } })
      .select(["-password", "-hashedKey"])
      .exec();
  } else {
    users = await User.find({ accounts: accountIdFilter })
      .populate("role", null, { slug: { $in: slugs } })
      .select(["-password", "-hashedKey"])
      .exec();
  }

  if (users && users.length > 0) {
    users = _.filter(users, function (value) {
      return value.role !== null;
    });
  }
  if (roleName != "super") {
    const latestUsers = [user].concat(users);
    return latestUsers;
  } else {
    return users;
  }
};

const changeUserPassword = async (id, data) => {
  let user = await User.findById(id);
  return new Promise((resolve, reject) => {
    bcrypt.compare(data.oldPassword, user.password, async (err, res) => {
      if (res) {
        user.password = data.password;
        await user.save();
        resolve(user);
      } else {
        reject({ status: 401, message: "Old password not matched" });
      }
    });
  });
};

const updateUserPassword = async (id, data) => {
  let user = await User.findById(id);
  user.password = data.password;
  user.timeZone = data.timeZone;
  user.requirePasswordReset = false;
  user.accepted = true;
  if (data.avatar) {
    user.avatar = data.avatar;
  }
  await user.save();
  return user;
};

const updateUserProfile = async (id, data) => {
  let user = await User.findById(id);
  user.fullName = data.fullName;
  user.jobTitle = data.jobTitle;
  if (data.avatar) {
    user.avatar = data.avatar;
  }
  await user.save();
  // let userUpdated = await User.findById(id);
  let userUpdated = await User.findById(id)
    .populate("role")
    .select(["-password", "-hashedKey"])
    .exec();
  return userUpdated;
};

const filterData = async (arr, callback) => {
  const fail = Symbol();
  return (
    await Promise.all(
      arr.map(async (item) => ((await callback(item)) ? item : fail))
    )
  ).filter((i) => i !== fail);
};

const syncUserAlertReport = async (userId) => {
  const userAlertReport = await AlertReport.find({
    userId: mongoose.Types.ObjectId(userId),
  });

  if (userAlertReport && userAlertReport.length > 0) {
    let user = await User.findById(userId).select(["_id", "facilities", "accounts"]);

    await filterData(userAlertReport, async (alertReport) => {
      let facilityId = alertReport.facilityId;
      const facilitySelected = _.find(user.facilities, {
        facilityId: facilityId,
      });

      if (user.accounts && user.accounts.length > 0) {
        const accountsId = user.accounts.map((ele) => ele.accountId?.toString());

        if (_.includes(accountsId, alertReport.accountId.toString())) {
          if (facilitySelected && (facilitySelected.read || facilitySelected.write)) {
            console.log("here not need to update the facility");
          } else {
            await AlertReport.findByIdAndDelete(alertReport?._id);
            console.log("delete alerts report");
          }
        } else {
          await AlertReport.findByIdAndDelete(alertReport?._id);
          console.log("delete alerts report because of account not exists");
        }
      }
    });
  }
}

const syncUserReport = async (userId) => {
  const userReportsSubscriptions = await ReportsSubscriptions.find({
    userId: mongoose.Types.ObjectId(userId),
  }).select(["facilityIds", "userId", "_id"]);

  if (userReportsSubscriptions && userReportsSubscriptions.length > 0) {
    let user = await User.findById(userId).select(["_id", "facilities"]);

    await filterData(userReportsSubscriptions, async (reportSubscription) => {
      if (
        reportSubscription.facilityIds &&
        reportSubscription.facilityIds.length > 0
      ) {
        let facilityIds = reportSubscription.facilityIds;

        await filterData(
          reportSubscription.facilityIds,
          async (reportFacilityId) => {
            const facilitySelected = _.find(user.facilities, {
              facilityId: reportFacilityId,
            });
            if (
              facilitySelected &&
              (facilitySelected.read || facilitySelected.write)
            ) {
              console.log("here not need to update the facility");
            } else {
              const index = facilityIds.indexOf(reportFacilityId.toString());
              if (index > -1) {
                facilityIds.splice(index, 1);
              }
            }
          }
        );

        facilityIds = facilityIds.map((e) => mongoose.Types.ObjectId(e));
        if (facilityIds && facilityIds.length > 0) {
          await ReportsSubscriptions.updateOne(
            { _id: reportSubscription._id },
            {
              $set: {
                facilityIds: facilityIds,
              },
            }
          );
        } else {
          await ReportsSubscriptions.findByIdAndDelete(reportSubscription?._id);
        }
      }
    });
  }
};

const getFacilityOptions = async (req) => {
  const { accountid } = req.headers;

  let search = {};
  if (accountid) {
    search.accountId = mongoose.Types.ObjectId(accountid);
  }

  let a = await await Facility.aggregate([
    { $match: { ...search } },
    {
      $lookup: {
        from: "accounts",
        localField: "accountId",
        foreignField: "_id",
        as: "account",
      },
    },

    { $unwind: { path: "$account", preserveNullAndEmptyArrays: true } },
    { $group: { _id: "$account", facilities: { $push: "$$ROOT" } } },
  ]);
  return a;
};

const getAllUsersCount = async () => {
  return await User.count();
};

const getAllUsersByAccount = async (req) => {
  // Fixed: Use correct param name, handle missing user, and remove debug logs
  const accountId = req.headers.accountid;
  if (!accountId) {
    return [];
  }

  const excludeUserId = req?.user?._id;
  const accountIdFilter = { $elemMatch: { accountId: { $in: [accountId] } } };
  let query = { accounts: accountIdFilter };

  const users = await User.find(query)
    .select(["-password", "-hashedKey"])
    .exec();

  const superAdminUsers = await User.find({ accountsId: { $eq: mongoose.Types.ObjectId(accountId) } })
    .populate("role")
    .select(["-password", "-hashedKey"])
    .exec();

  if (superAdminUsers.length > 0) {
    let superAdminUsersArr = superAdminUsers.filter(u => u?.role?.slug === AUTH_ROLES.SUPER);
    users.push(...superAdminUsersArr);
  }

  // Filter out current user from the list
  if (excludeUserId) {
    return users.filter(user => user._id.toString() !== excludeUserId.toString());
  }

  return users;
};

module.exports = {
  getAllUsersCount,
  createUser,
  loginUser,
  getUsersByAccount,
  updateUserPassword,
  getFacilityOptions,
  updateUserProfile,
  changeUserPassword,
  forgotPassword,
  updateNewPassword,
  syncUserReport,
  syncUserAlertReport,
  getUsersByAccountForNote,
  getAllUsersByAccount,
  shareUniversalTabsWithNewUser
};
