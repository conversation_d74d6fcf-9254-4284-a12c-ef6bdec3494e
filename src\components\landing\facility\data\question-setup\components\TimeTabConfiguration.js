import React, { useState } from 'react';
import {
    Box,
    FormControl,
    TextField,
    Typography,
    Button,
    IconButton,
    Chip,
    Grid,
    Card,
    CardContent,
    FormControlLabel,
    Radio,
    RadioGroup,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { TIME_RANGE_TYPE, DEFAULT_TIME_RANGES, HOURLY_TIME_RANGES } from '../../../../../../types/question.type';

const TimeTabConfiguration = ({ setQuestionValues, questionValues }) => {
    const [newTimeRange, setNewTimeRange] = useState({ label: '', startTime: '', endTime: '' });
    const [showAllHourlyRanges, setShowAllHourlyRanges] = useState(false);

    const handleTimeRangeTypeChange = (event) => {
        const type = event.target.value;
        setQuestionValues(prevValues => ({
            ...prevValues,
            timeRangeType: type,
            customQuestionOptions: type === TIME_RANGE_TYPE.CUSTOM ? DEFAULT_TIME_RANGES : [],
        }));
    };

    const handleAddCustomTimeRange = () => {
        if (newTimeRange.label && newTimeRange.startTime && newTimeRange.endTime) {
            setQuestionValues(prevValues => ({
                ...prevValues,
                customQuestionOptions: [...(prevValues.customQuestionOptions || []), { ...newTimeRange }],
            }));
            setNewTimeRange({ label: '', startTime: '', endTime: '' });
        }
    };

    const handleRemoveTimeRange = (index) => {
        setQuestionValues(prevValues => ({
            ...prevValues,
            customQuestionOptions: prevValues.customQuestionOptions.filter((_, i) => i !== index),
        }));
    };

    const handleEditTimeRange = (index, field, value) => {
        setQuestionValues(prevValues => ({
            ...prevValues,
            customQuestionOptions: prevValues.customQuestionOptions.map((range, i) =>
                i === index ? { ...range, [field]: value } : range
            ),
        }));
    };

    const formatTime = (time) => {
        const hour = parseInt(time.split(':')[0]);
        const minute = time.split(':')[1];
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
        return `${displayHour}:${minute} ${ampm}`;
    };

    return (
        <Box sx={{ mt: 2 }}>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <AccessTimeIcon sx={{ mr: 1 }} />
                Time Tab Configuration
            </Typography>

            {/* Time Range Type Selection */}
            <FormControl fullWidth margin="normal">
                <Typography variant="subtitle1" sx={{ mb: 1 }}>
                    Time Range Mode
                </Typography>
                <RadioGroup
                    value={questionValues.timeRangeType || TIME_RANGE_TYPE.HOURLY}
                    onChange={handleTimeRangeTypeChange}
                    row
                >
                    <FormControlLabel
                        value={TIME_RANGE_TYPE.HOURLY}
                        control={<Radio />}
                        label="Hourly (Default)"
                    />
                    <FormControlLabel
                        value={TIME_RANGE_TYPE.CUSTOM}
                        control={<Radio />}
                        label="Custom Time Ranges"
                    />
                </RadioGroup>
            </FormControl>

            {/* Hourly Preview */}
            {questionValues.timeRangeType === TIME_RANGE_TYPE.HOURLY && (
                <Card sx={{ mt: 2, backgroundColor: '#f5f5f5' }}>
                    <CardContent>
                        <Typography variant="subtitle2" sx={{ mb: 2 }}>
                            Default Hourly Time Ranges Preview (24 ranges total):
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                            {HOURLY_TIME_RANGES.slice(0, showAllHourlyRanges ? HOURLY_TIME_RANGES.length : 6).map((hour, index) => (
                                <Chip
                                    key={index}
                                    label={hour.label}
                                    size="small"
                                    variant="outlined"
                                    sx={{ fontSize: '0.75rem' }}
                                />
                            ))}
                            {!showAllHourlyRanges && (
                                <Chip 
                                    label="... and 18 more hourly ranges" 
                                    size="small" 
                                    color="primary"
                                    onClick={() => setShowAllHourlyRanges(true)}
                                    sx={{ cursor: 'pointer' }}
                                />
                            )}
                        </Box>
                    </CardContent>
                </Card>
            )}

            {/* Custom Time Ranges */}
            {questionValues.timeRangeType === TIME_RANGE_TYPE.CUSTOM && (
                <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 2 }}>
                        Custom Time Ranges
                    </Typography>

                    {/* Existing Custom Time Ranges */}
                    {questionValues.customQuestionOptions?.map((range, index) => (
                        <Card key={index} sx={{ mb: 2, border: '1px solid #e0e0e0' }}>
                            <CardContent>
                                <Grid container spacing={2} alignItems="center">
                                    <Grid item xs={12} sm={3}>
                                        <TextField
                                            label="Range Label"
                                            fullWidth
                                            size="small"
                                            value={range.label}
                                            onChange={(e) => handleEditTimeRange(index, 'label', e.target.value)}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={3}>
                                        <TextField
                                            label="Start Time"
                                            type="time"
                                            fullWidth
                                            size="small"
                                            value={range.startTime}
                                            onChange={(e) => handleEditTimeRange(index, 'startTime', e.target.value)}
                                            InputLabelProps={{ shrink: true }}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={3}>
                                        <TextField
                                            label="End Time"
                                            type="time"
                                            fullWidth
                                            size="small"
                                            value={range.endTime}
                                            onChange={(e) => handleEditTimeRange(index, 'endTime', e.target.value)}
                                            InputLabelProps={{ shrink: true }}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={2}>
                                        <Typography variant="body2" color="textSecondary">
                                            {formatTime(range.startTime)} - {formatTime(range.endTime)}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={1}>
                                        <IconButton
                                            onClick={() => handleRemoveTimeRange(index)}
                                            color="error"
                                            size="small"
                                        >
                                            <DeleteIcon />
                                        </IconButton>
                                    </Grid>
                                </Grid>
                            </CardContent>
                        </Card>
                    ))}

                    {/* Add New Time Range */}
                    <Card sx={{ border: '2px dashed #ccc', backgroundColor: '#fafafa' }}>
                        <CardContent>
                            <Typography variant="subtitle2" sx={{ mb: 2 }}>
                                Add New Time Range
                            </Typography>
                            <Grid container spacing={2} alignItems="center">
                                <Grid item xs={12} sm={3}>
                                    <TextField
                                        label="Range Label"
                                        fullWidth
                                        size="small"
                                        value={newTimeRange.label}
                                        onChange={(e) => setNewTimeRange(prev => ({ ...prev, label: e.target.value }))}
                                        placeholder="e.g., Morning Shift"
                                    />
                                </Grid>
                                <Grid item xs={12} sm={3}>
                                    <TextField
                                        label="Start Time"
                                        type="time"
                                        fullWidth
                                        size="small"
                                        value={newTimeRange.startTime}
                                        onChange={(e) => setNewTimeRange(prev => ({ ...prev, startTime: e.target.value }))}
                                        InputLabelProps={{ shrink: true }}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={3}>
                                    <TextField
                                        label="End Time"
                                        type="time"
                                        fullWidth
                                        size="small"
                                        value={newTimeRange.endTime}
                                        onChange={(e) => setNewTimeRange(prev => ({ ...prev, endTime: e.target.value }))}
                                        InputLabelProps={{ shrink: true }}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={3}>
                                    <Button
                                        variant="contained"
                                        color="primary"
                                        onClick={handleAddCustomTimeRange}
                                        startIcon={<AddIcon />}
                                        disabled={!newTimeRange.label || !newTimeRange.startTime || !newTimeRange.endTime}
                                        fullWidth
                                    >
                                        Add Range
                                    </Button>
                                </Grid>
                            </Grid>
                        </CardContent>
                    </Card>
                </Box>
            )}
        </Box>
    );
};

export default TimeTabConfiguration; 