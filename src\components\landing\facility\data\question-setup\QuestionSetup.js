import update from "immutability-helper";
import _, { cloneDeep } from "lodash";
import { useEffect, useMemo, useState } from "react";
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import SettingsIcon from '@mui/icons-material/Settings';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import DragSVG from "../../../../../assets/svgs/drag.svg";
import axios from "../../../../../axios";
import { getAllQuestions } from "../../../../../services/api/question.api";
import { isFullAccess, isOnlyHospitalTabAccess, selectedTabAccess } from "../../../../../utilis/common";
import AlertDialog from "../../../../shared/AlertDialog";
import Button from "../../../../ui/button/Button";
import styles from "./QuestionSetup.module.scss";
import { updateQuestionLabel } from "../../../../../services/api/data-input.api";
import AddQuestionDialog from "./AddQuestion";
import { Dialog, DialogContent, DialogTitle, Divider, IconButton, FormControl, InputLabel, Select, MenuItem, Menu, ListItemIcon, ListItemText } from "@mui/material";
import { styled } from "@mui/system";
import CloseIcon from '@mui/icons-material/Close';
import { useSelector } from "react-redux";
import { AUTH_ROLES } from "../../../../../types/auth.type";
import useResetDynamicCards from "../../../../hooks/useResetDynamicCards";
import { PAGE_TYPE } from "../../../../../types/pages.type";
import { QUESTION_INPUT_TYPE, QUESTIONS_TEMPLATE_TYPE, TIME_RANGE_TYPE } from "../../../../../types/question.type";
import QuestionsPreview from "./QuestionsPreview";
import AutoFillSettingsDialog from "./AutoFillSettingsDialog";


const initialValues = {
	label: "",
	tableLabel: "",
	isCustom: false,
	customQuestionInputType: "",
	customQuestionOptions: [],
	numberRangeType: null,
	isArchived: false,
	timeRangeType: TIME_RANGE_TYPE.HOURLY,
	templateType: QUESTIONS_TEMPLATE_TYPE.DX_CARD
}

const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
	display: 'flex',
	justifyContent: 'space-between',
	alignItems: 'center',
	padding: theme.spacing(2, 3),
}));

const QuestionSetup = ({ close }) => {
	const { permission } = useSelector(
		({ auth, account, facility, permission, }) => ({
			auth,
			account,
			facility,
			permission
		})
	);

	const isOnlyHospitalDashboard = useMemo(() => {
		return isOnlyHospitalTabAccess();
	}, []);

	const transferOptions = useMemo(() => {
		const selectedTab = selectedTabAccess();
		return [
			...(_.includes(selectedTab, PAGE_TYPE.HOSPITAL) ? [{ label: "Hospital Transfer", value: "hospitalTransfer" }] : []),
			...(_.includes(selectedTab, PAGE_TYPE.COMMUNITY_TRANSFER) ? [
				{ label: "Safe Discharge", value: "safeDischarge" },
				{ label: "SNF", value: "SNF" },
				{ label: "AMA", value: "AMA" }
			] : []),
			...(_.includes(selectedTab, PAGE_TYPE.DECEASED) ? [{ label: "Deceased", value: "deceased" }] : []),
		];
	}, []);

	const [list, setList] = useState(undefined);
	const [dragDisabled, setDragDisabled] = useState(false);
	const [showAddQ, setShowAddQ] = useState(false);
	const [confirmDialog, setConfirmDialog] = useState(false);
	const [type, setType] = useState("admission");
	const [transferType, setTransferType] = useState("hospitalTransfer");
	const [editQuestion, setEditQuestion] = useState(undefined);
	const [deleteIdx, setDeleteIdx] = useState(undefined);
	const [loading, setLoading] = useState(false);
	const [questionValues, setQuestionValues] = useState(initialValues);
	const [showAutoFillDialog, setShowAutoFillDialog] = useState(false);
	const [selectedQuestionForAutoFill, setSelectedQuestionForAutoFill] = useState(null);
	const [menuAnchorEl, setMenuAnchorEl] = useState(null);
	const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(null);
	const resetDynamicCards = useResetDynamicCards();

	const resetQuestionValues = () => {
		setQuestionValues(initialValues)
	}

	useEffect(() => {
		if (isOnlyHospitalDashboard) {
			setType("transfer");
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	const onDragEnd = async (droppedItem, b) => {
		if (!droppedItem.destination) return;
		var updatedList = cloneDeep(list);
		const [reorderedItem] = updatedList.splice(droppedItem.source.index, 1);
		updatedList.splice(droppedItem.destination.index, 0, reorderedItem);

		let modifiedList = await updatedList.map((item, index) => {
			item.order.order = index;
			return item.order;
		});

		setDragDisabled(true);
		await axios.put(`/api/question/set/?id=${list[0]._id}`, modifiedList);
		setDragDisabled(false);
		setList(updatedList);
	};

	const getQuestions = async () => {
		const latestType = isOnlyHospitalDashboard && type !== "return" ? "transfer" : type;
		const questions = await getAllQuestions({
			facilityid: localStorage.getItem("facilityId"),
			forType: latestType,
			...(latestType === "transfer" && { forTransferType: transferType }),
		});
		// const questions = await axios.get(
		//   `/api/question/set/?forType=${type}&facilityid=${localStorage.getItem("facilityId")}${type === "transfer" ? `&forTransferType=${transferType}` : ""
		//   }`
		// );
		
		setList(questions);
	};

	const saveQuestion = async (isCustomQuestion, updatedValues = null) => {
		const questionValuesData = updatedValues ? updatedValues : questionValues;		
		let newQuestion;
		setLoading(true);
		if (editQuestion?._id) {
			const latestType = isOnlyHospitalDashboard && type !== "return" ? "transfer" : type;

			let updatedQ = await updateQuestionLabel(
				{
					label: questionValuesData.label,
					tableLabel: questionValuesData.tableLabel,
					isArchived: questionValuesData?.isArchived ?? false,
					templateType: questionValuesData?.templateType,
					timeRangeType: questionValuesData?.timeRangeType,
					forType: latestType,
					...(isCustomQuestion && { customData: questionValuesData }),
					...(latestType === "transfer" && { forTransferType: transferType }),
				},
				editQuestion._id
			);

			let indexAAA = list.findIndex(item => item.question._id === editQuestion._id);

			update(list, {
				[indexAAA]: {
					question: {
						$set: updatedQ.data,
					},
				},
			});
			resetDynamicCards();
			getQuestions();
			setLoading(false);
			setEditQuestion(undefined);
			resetQuestionValues()
			setShowAddQ(false);
		} else {
			newQuestion = await axios.post("/api/question/set", {
				facilityId: localStorage.getItem("facilityId"),
				label: questionValuesData.label,
				tableLabel: questionValuesData?.tableLabel,
				isArchived: questionValuesData?.isArchived ?? false,
				forType: type,
				forTransferType: transferType,
				active: true,
				type: "text",
				accessor: questionValuesData?.label?.replace(/\s/g, ""),
				allowAfter: true,
				isEditable: true,
				accountId: localStorage.getItem("accountId"),
				customData: questionValuesData,
				templateType: questionValuesData?.templateType,
			});
			let modifiedList = await list.map((item, index) => {
				item.order.order = index;
				return item.order;
			});

			const newOrder = await axios.put(`/api/question/set/?id=${list[0]._id}`, [
				...modifiedList,
				{ order: list.length, questionId: newQuestion.data._id },
			]);
			resetDynamicCards();
			getQuestions(newOrder.data);
			setLoading(false);
			resetQuestionValues()
			setShowAddQ(false);
		}
	};

	useEffect(() => {
		getQuestions();
		// eslint-disable-next-line
	}, []);

	useEffect(() => {
		getQuestions();
		// eslint-disable-next-line
	}, [type, transferType]);

	const deleteQuestion = async (id, idx, isArchived = false) => {
		await axios.delete(`/api/question/set/?id=${id}${isArchived ? '&archived=true' : ''}`);

		// remove deleted question from list
		let updatedList = update(list, {
			$splice: [[idx, 1]],
		});

		let modifiedList = await updatedList.map((item, index) => {
			item.order.order = index;
			return item.order;
		});

		const newOrder = await axios.put(`/api/question/set/?id=${list[0]._id}`, modifiedList);
		resetDynamicCards();
		getQuestions(newOrder.data);
	};

	const handleClose = () => {
		close();
		setEditQuestion(undefined);
	};

	const handleCustomQuestion = () => {
		const isSuperOrAdmin = [AUTH_ROLES.SUPER, AUTH_ROLES.ADMIN].includes(permission?.role);
		if (!isSuperOrAdmin) return false;

		return editQuestion ? !!editQuestion.isCustom : true;
	};

	const [isArchivedDialog, setIsArchivedDialog] = useState(false);
	const [questionPreview, setQuestionPreview] = useState(null);

	const handleViewQuestion = async (question) => {
		setQuestionPreview(question);
	}

	const handleSaveAutoFillSettings = async (autoFillData) => {
		if (!selectedQuestionForAutoFill) return;

		try {
			setLoading(true);

			// The AutoFillSettingsDialog now handles the API call directly
			// We just need to refresh the questions list after save
			getQuestions();
			setLoading(false);
			setSelectedQuestionForAutoFill(null);
		} catch (error) {
			console.error('Error refreshing questions after auto-fill save:', error);
			setLoading(false);
		}
	}

	// Menu handlers
	const handleMenuClick = (event, questionIndex) => {
		setMenuAnchorEl(event.currentTarget);
		setSelectedQuestionIndex(questionIndex);
	};

	const handleMenuClose = () => {
		setMenuAnchorEl(null);
		setSelectedQuestionIndex(null);
	};

	const handleEditQuestion = (question) => {
		setQuestionValues({
			label: question.order?.label || question.label,
			tableLabel: question.order?.tableLabel || question.tableLabel,
			customQuestionInputType: question?.customQuestionInputType,
			customQuestionOptions: question?.customQuestionOptions,
			templateType: question?.templateType ?? QUESTIONS_TEMPLATE_TYPE.DX_CARD,
			numberRangeType: question?.customQuestionInputType === QUESTION_INPUT_TYPE.NUMBER_RANGE ? question?.numberRangeType : null,
			timeRangeType: question?.customQuestionInputType === QUESTION_INPUT_TYPE.TIME_TAB_RANGE ? question?.timeRangeType : null,
			autoFillEnabled: question?.autoFillEnabled || false,
			sourceTransitionType: question?.sourceTransitionType || '',
			sourceQuestionId: question?.sourceQuestionId || ''
		});
		setEditQuestion(question);
		setShowAddQ(true);
		handleMenuClose();
	};

	const handleAutoFillSettings = (question) => {
		setSelectedQuestionForAutoFill(question);
		setShowAutoFillDialog(true);
		handleMenuClose();
	};

	// Helper function to check if question type supports auto-fill
	const supportsAutoFill = (question) => {
		// No auto-fill for questions with fixed options (dropdown, yes/no, etc.)
		const fixedOptionTypes = [
			QUESTION_INPUT_TYPE.LIMITED_ANSWERS,
			QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS,
			QUESTION_INPUT_TYPE.TIME_TAB_RESIDENT_LIST,
		];
		return !fixedOptionTypes.includes(question?.customQuestionInputType);
	};

	return (
		<>
			{!!questionPreview && (
				<QuestionsPreview
					handleClose={() => {
						setQuestionPreview(null);
					}}
					onClose={() => setQuestionPreview(null)}
					question={questionPreview}
					isPreview={true}
				/>
			)}
			{!!showAutoFillDialog && selectedQuestionForAutoFill && (
				<AutoFillSettingsDialog
					open={showAutoFillDialog}
					onClose={() => {
						setShowAutoFillDialog(false);
						setSelectedQuestionForAutoFill(null);
					}}
					question={selectedQuestionForAutoFill}
					onSave={handleSaveAutoFillSettings}
					latestType={isOnlyHospitalDashboard && type !== "return" ? "transfer" : type}
					transferType={transferType}
				/>
			)}
			{!!isArchivedDialog && (
				<AlertDialog
					{...(editQuestion?._id && { isArchived: questionValues?.isArchived ?? false })}
					content="Archived question."
					handleClose={async () => {
						const updatedValues = {
							...questionValues,
							isArchived: false,
						};
						await setQuestionValues(updatedValues);
						setIsArchivedDialog(false);
						saveQuestion(handleCustomQuestion(), updatedValues);
					}}
					withYesNo={true}
					handleSubmit={async () => {
						const updatedValues = {
							...questionValues,
							isArchived: true,
						};
						await setQuestionValues(updatedValues);
						setIsArchivedDialog(false);
						setConfirmDialog(false);
						saveQuestion(handleCustomQuestion(), updatedValues);
					}}
				/>
			)}
			{!!confirmDialog && (
				<AlertDialog
					content="New Question will be display for all account facility."
					handleClose={() => setConfirmDialog(false)}
					handleSubmit={() => {
						setConfirmDialog(false);
						setIsArchivedDialog(true);
					}}
				/>
			)}
			<Dialog open={true} maxWidth="xl" fullWidth={true}>
				<StyledDialogTitle>
					{editQuestion?._id ? 'Edit Question' : 'Add Question'}
					<IconButton aria-label="close" onClick={handleClose}>
						<CloseIcon />
					</IconButton>
				</StyledDialogTitle>
				<Divider />
				<DialogContent>
					{showAddQ && (
						<AddQuestionDialog
							open={showAddQ}
							setIsArchivedDialog={setIsArchivedDialog}
							onClose={() => setShowAddQ(false)}
							onSave={saveQuestion}
							refreshQuestions={getQuestions}
							editQuestion={editQuestion}
							loading={loading}
							setEditQuestion={setEditQuestion}
							setQuestionValues={setQuestionValues}
							questionValues={questionValues}
							setConfirmDialog={setConfirmDialog}
							resetQuestionValues={resetQuestionValues}
							isCustomQuestion={handleCustomQuestion()}
							latestType={type}
							transferType={transferType}
							currentQuestions={list}
						/>
					)}
					<div className={styles.questionSetup}>
						<div className={styles.content}>
							<div className={`df aic ffmar fs15 fw500 p-r-20 p-l-20 ${styles.tabsWrpr}`}>
								{_.includes(selectedTabAccess(), PAGE_TYPE.ADMISSION) && (
									<>
										<div
											className={`df acc ${styles.tab} ${type === "admission" ? styles.active : ""}`}
											onClick={() => setType("admission")}
										>
											Admissions
										</div>
									</>
								)}
								{isFullAccess() && (
									<div
										className={`df acc ${styles.tab} ${type === "return" ? styles.active : ""}`}
										onClick={() => setType("return")}
									>
										Returns
									</div>
								)}
								{[PAGE_TYPE.HOSPITAL, PAGE_TYPE.COMMUNITY_TRANSFER, PAGE_TYPE.DECEASED].some(ele => selectedTabAccess().includes(ele)) && (
									<div
										className={`df acc ${styles.tab} ${type === "transfer" ? styles.active : ""}`}
										onClick={() => setType("transfer")}
									>
										Transfers
									</div>
								)}
								{type === "transfer" ? (
									<FormControl sx={{ minWidth: 275, ml: 2 }}>
										<InputLabel>Transfer Type</InputLabel>
										<Select
											value={transferType}
											onChange={(e) => setTransferType(e.target.value)}
											label="Transfer Type"
											size="small"
										>
											{transferOptions.map((option) => (
												<MenuItem key={option.value} value={option.value}>
													{option.label}
												</MenuItem>
											))}
										</Select>
									</FormControl>
								) : undefined}

								<div className={`mla`}>
									<Button text="Add Question" buttonStyle="theme" size="medium" action={() => {
										setLoading(false);
										resetQuestionValues();
										setShowAddQ(!showAddQ)
									}} />
								</div>
							</div>
							<p
								style={{
									padding: "14px 22px 10px",
									fontStyle: "italic",
								}}
							>
								Click and drag questions to adjust the question order
							</p>
							{list && (
								<div className={styles.questionList}>
									<DragDropContext onDragEnd={onDragEnd}>
										<Droppable droppableId="droppable">
											{(provided, snapshot) => (
												<div {...provided.droppableProps} ref={provided.innerRef}>
													{list.map((itm, idx) => (
														<Draggable
															isDragDisabled={dragDisabled}
															draggableId={`id-${idx}`}
															key={`${idx}-${idx}`}
															index={idx}
														>
															{(provided, snapshot) => (
																<p
																	className={`ffmsb df aic ${styles.line} ${snapshot.isDragging ? styles.dragging : ""}`}
																	ref={provided.innerRef}
																	{...provided.draggableProps}
																	{...provided.dragHandleProps}
																>
																	<span className={styles.iconWrpr}>
																		<DragSVG />
																	</span>
																	<span className={styles.num}>{idx + 1}.</span> {itm.order?.label || itm.question.label}
																	{provided.placeholder}
																	<div className={`df aic mla`}>
																		<IconButton
																			className={`m-r-15 ${styles.action}`}
																			onClick={(event) => handleMenuClick(event, idx)}
																			size="small"
																			sx={{
																				padding: '8px',
																				borderRadius: '6px',
																				backgroundColor: 'rgba(0, 0, 0, 0.04)',
																				'&:hover': {
																					backgroundColor: 'rgba(0, 0, 0, 0.08)',
																				},
																				transition: 'all 0.2s ease-in-out'
																			}}
																		>
																			<MoreVertIcon sx={{ fontSize: '18px', color: '#666' }} />
																		</IconButton>
																		{deleteIdx === idx && (
																			<div className={`df aic ${styles.confirmDelete}`}>
																				<button
																					className={`m-r-10`}
																					onClick={() => {
																						setDeleteIdx(undefined);
																					}}
																				>
																					Cancel
																				</button>
																				<button
																					className={styles.confirmDeleteBtn}
																					onClick={() => {
																						deleteQuestion(itm.question._id, idx, itm?.question?.isArchived);
																						setDeleteIdx(undefined);
																					}}
																				>
																					Confirm
																				</button>
																			</div>
																		)}
																	</div>
																</p>
															)}
														</Draggable>
													))}
												</div>
											)}
										</Droppable>
									</DragDropContext>
								</div>
							)}
						</div>
					</div>
				</DialogContent>
			</Dialog>

			{/* Material-UI Menu for question actions */}
			<Menu
				anchorEl={menuAnchorEl}
				open={Boolean(menuAnchorEl)}
				onClose={handleMenuClose}
				PaperProps={{
					elevation: 3,
					sx: {
						minWidth: 180,
						borderRadius: 2,
						mt: 1,
						'& .MuiMenuItem-root': {
							px: 2,
							py: 0.5,
							borderRadius: 1,
							mx: 1,
							my: 0.25,
							fontSize: '13px',
							minHeight: '32px',
							'&:hover': {
								backgroundColor: 'rgba(0, 0, 0, 0.04)',
							},
						},
					},
				}}
			>
				{selectedQuestionIndex !== null && list[selectedQuestionIndex] && (
					<>
						{!list[selectedQuestionIndex]?.question?.isArchived ? (
							<>
								<MenuItem
									dense
									onClick={() => handleEditQuestion(list[selectedQuestionIndex].question)}
								>
									<ListItemIcon sx={{ minWidth: 36 }}>
										<EditIcon fontSize="small" />
									</ListItemIcon>
									<ListItemText primary="Edit" />
								</MenuItem>
								{/* Only show Auto Fill Settings if question type supports it */}
								{supportsAutoFill(list[selectedQuestionIndex].question) && (
									<>
										<Divider sx={{ my: 0.25 }} />
										<MenuItem
											dense
											onClick={() => handleAutoFillSettings(list[selectedQuestionIndex].question)}
										>
											<ListItemIcon sx={{ minWidth: 36 }}>
												<SettingsIcon fontSize="small" />
											</ListItemIcon>
											<ListItemText primary="Auto Fill Settings" />
										</MenuItem>
									</>
								)}
							</>
						) : (
							<MenuItem
								dense
								onClick={() => handleViewQuestion(list[selectedQuestionIndex].question)}
							>
								<ListItemIcon sx={{ minWidth: 36 }}>
									<VisibilityOutlinedIcon fontSize="small" />
								</ListItemIcon>
								<ListItemText primary="View" />
							</MenuItem>
						)}
						{list[selectedQuestionIndex].question.isEditable && (
							<>
								<Divider sx={{ my: 0.25 }} />
								<MenuItem
									dense
									onClick={() => {
										setDeleteIdx(selectedQuestionIndex);
										handleMenuClose();
									}}
									sx={{ color: 'error.main' }}
								>
									<ListItemIcon sx={{ minWidth: 36, color: 'error.main' }}>
										<DeleteIcon fontSize="small" />
									</ListItemIcon>
									<ListItemText primary="Delete" />
								</MenuItem>
							</>
						)}
					</>
				)}
			</Menu>
		</>
	);
};

export default QuestionSetup;
