const { authenticate } = require("../middleware/authenticate");
const { 
    getDynamicDataTabByPage, 
    createDynamicDataTab, 
    deleteDynamicDataTab, 
    updateDynamicDataTab,    
    getQuestionsData
 } = require("../helpers/dynamicDataTab");

const route = require("express").Router();

// Create new subscription
route.post("/", authenticate, async (req, res) => {
  try {
    let dynamicDataTab = await createDynamicDataTab(req);
    res.send(dynamicDataTab);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.get("/questions", authenticate, async (req, res) => {
  try {
    const dynamicDataTabs = await getQuestionsData(req);
    res.send(dynamicDataTabs);
  } catch (error) {    
    res.status(500).send(error);
  }
});

route.get("/all", authenticate, async (req, res) => {
  try {
    const dynamicDataTabs = await getDynamicDataTabByPage(req);
    res.send(dynamicDataTabs);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.delete("/:id", authenticate, async (req, res) => {
  try {
    const dynamicDataTab = await deleteDynamicDataTab(req.params.id);
    res.send(dynamicDataTabs);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.put("/:id", authenticate, async (req, res) => {
  try {
    const dynamicDataTabs = await updateDynamicDataTab(req.params.id, req.body);
    res.send(dynamicDataTabs);
  } catch (error) {
    res.status(500).send(error);
  }
});

module.exports = route;
