import React, { useState, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import CloseIcon from '@mui/icons-material/Close';
import CircularProgress from '@mui/material/CircularProgress';
import IconButton from '@mui/material/IconButton';
import { styled } from '@mui/material/styles';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import InputLabel from '@mui/material/InputLabel';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import { QUESTION_INPUT_TYPE, QUESTION_TYPE_OPTIONS, QUESTIONS_TEMPLATES } from '../../../../../types/question.type';
import NumberRangeType from './components/NumberRangeType';
import LimitedAnswersOptions from './components/LimitedAnswersOptions';
import NumberRangeWithUnlimitedAnswer from './components/NumberRangeWithUnlimitedAnswer';
import QuestionErrorList from './components/QuestionErrorList';
import TimeTabConfiguration from './components/TimeTabConfiguration';
import { validateQuestionValues } from '../../../../../utilis/common';
import { checkDuplicateQuestionLabel } from '../../../../../services/api/data-input.api';
import { getAutoFillSourceQuestionsAPI } from '../../../../../services/api/question.api';
import _ from 'lodash';
import { Box } from '@mui/material';
import AccountQuestions from './AccountQuestions';
import TemplateInfoDialog from './components/TemplateInfoDialog';
import WebIcon from '@mui/icons-material/Web';

const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(2, 3),
}));

const AddQuestionDialog = ({
    open,
    onClose,
    onSave,
    editQuestion,
    loading,
    setEditQuestion,
    setQuestionValues,
    questionValues,
    setConfirmDialog,
    resetQuestionValues,
    isCustomQuestion,
    latestType,
    transferType,
    refreshQuestions,
    setIsArchivedDialog
}) => {
    const [infoOpen, setInfoOpen] = useState(false);
    const [errors, setErrors] = useState({});
    const [openErrorDialog, setOpenErrorDialog] = useState(false);
    const [accountQuestions, setAccountQuestions] = useState(false);

    // Auto-fill functionality states
    const [enableAutoFill, setEnableAutoFill] = useState(false);
    const [sourceTransitionType, setSourceTransitionType] = useState('');
    const [sourceQuestionId, setSourceQuestionId] = useState('');
    const [availableSourceQuestions, setAvailableSourceQuestions] = useState([]);
    const [loadingSourceQuestions, setLoadingSourceQuestions] = useState(false);

    // useEffect(() => {
    //     alert(isCustomQuestion);
    // }, [isCustomQuestion]);

    // Define transition type options for auto-fill
    const transitionTypeOptions = [
        { value: 'admission', label: 'Admission', forType: 'admission' },
        { value: 'readmission', label: 'Readmission', forType: 'admission' },
        { value: 'return', label: 'Return', forType: 'return' },
        { value: 'hospitalTransfer', label: 'Hospital Transfer', forType: 'transfer', forTransferType: 'hospitalTransfer' },
        { value: 'safeDischarge', label: 'Safe Discharge', forType: 'transfer', forTransferType: 'safeDischarge' },
        { value: 'SNF', label: 'SNF', forType: 'transfer', forTransferType: 'SNF' },
        { value: 'AMA', label: 'AMA', forType: 'transfer', forTransferType: 'AMA' },
        { value: 'deceased', label: 'Deceased', forType: 'transfer', forTransferType: 'deceased' },
    ];

    // Helper function to check if question type supports auto-fill
    const supportsAutoFill = (inputType) => {
        // No auto-fill for questions with fixed options (dropdown, yes/no, etc.)
        const fixedOptionTypes = [
            QUESTION_INPUT_TYPE.LIMITED_ANSWERS,
            QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS,
        ];
        return !fixedOptionTypes.includes(inputType);
    };



    // Fetch source questions when transition type changes
    const fetchSourceQuestions = async (transitionType) => {
        if (!transitionType) {
            setAvailableSourceQuestions([]);
            return;
        }

        setLoadingSourceQuestions(true);
        try {
            const params = {
                facilityid: localStorage.getItem("facilityId"),
                sourceTransitionType: transitionType,
                targetQuestionType: questionValues.customQuestionInputType
            };

            const questions = await getAutoFillSourceQuestionsAPI(params);
            setAvailableSourceQuestions(questions || []);
        } catch (error) {
            console.error('Error fetching source questions:', error);
            setAvailableSourceQuestions([]);
        } finally {
            setLoadingSourceQuestions(false);
        }
    };

    // Handle transition type change
    const handleTransitionTypeChange = (value) => {
        setSourceTransitionType(value);
        setSourceQuestionId(''); // Reset selected question
        fetchSourceQuestions(value);
    };

    // Handle auto-fill toggle
    const handleAutoFillToggle = (checked) => {
        setEnableAutoFill(checked);
        if (!checked) {
            // Reset auto-fill related fields
            setSourceTransitionType('');
            setSourceQuestionId('');
            setAvailableSourceQuestions([]);
            // Remove auto-fill data from question values
            handleInputChange('autoFillEnabled', false);
            handleInputChange('sourceTransitionType', '');
            handleInputChange('sourceQuestionId', '');
        } else {
            handleInputChange('autoFillEnabled', true);
        }
    };

    // Handle source question selection
    const handleSourceQuestionChange = (questionId) => {
        setSourceQuestionId(questionId);
        handleInputChange('sourceQuestionId', questionId);
    };

    // Update question values when auto-fill settings change
    useEffect(() => {
        if (enableAutoFill) {
            handleInputChange('sourceTransitionType', sourceTransitionType);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [sourceTransitionType, enableAutoFill]);

    // Initialize auto-fill states when editing an existing question
    useEffect(() => {
        if (editQuestion && questionValues.autoFillEnabled) {
            setEnableAutoFill(true);
            if (questionValues.sourceTransitionType) {
                setSourceTransitionType(questionValues.sourceTransitionType);
                fetchSourceQuestions(questionValues.sourceTransitionType);
            }
            if (questionValues.sourceQuestionId) {
                setSourceQuestionId(questionValues.sourceQuestionId);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [editQuestion, questionValues.autoFillEnabled]);

    // Clear auto-fill settings when question type changes
    useEffect(() => {
        // Only clear if auto-fill is currently enabled and question type doesn't support it
        if (enableAutoFill && !supportsAutoFill(questionValues.customQuestionInputType)) {
            console.log('Question type changed to one that doesn\'t support auto-fill, clearing auto-fill settings');
            handleAutoFillToggle(false);
        } else if (enableAutoFill && sourceTransitionType) {
            // If question type changed but still supports auto-fill, refresh source questions
            // to ensure compatibility with new type
            console.log('Question type changed, refreshing compatible source questions');
            fetchSourceQuestions(sourceTransitionType);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [questionValues.customQuestionInputType]);

    const handleClose = () => {
        resetQuestionValues();
        setEditQuestion(undefined);
        setAccountQuestions(false);
        // Reset auto-fill states
        setEnableAutoFill(false);
        setSourceTransitionType('');
        setSourceQuestionId('');
        setAvailableSourceQuestions([]);
        onClose();
    };

    const handleDuplicate = async (questionValues) => {
        let transferTypeArr = [transferType];
        if (transferType === "SNF" || transferType === "AMA" || transferType === "safeDischarge") {
            transferTypeArr = ["SNF", "AMA", "safeDischarge"]
        }

        let updatedQ = await checkDuplicateQuestionLabel(
            {
                label: questionValues.label,
                _id: editQuestion?._id || null,
                tableLabel: questionValues.tableLabel,
                forType: latestType,                
                ...latestType === "transfer" && { forTransferType: transferTypeArr }
            }
        );
        return updatedQ;
    }

    const handleSave = async () => {
        let validationErrors = {};

        // Validate label for non-custom questions
        if (!isCustomQuestion && !questionValues.label) {
            validationErrors.label = 'Label is required.';
        }
        if (questionValues.customQuestionInputType === QUESTION_INPUT_TYPE.TIME_TAB_RANGE && !questionValues.timeRangeType) {
            validationErrors.timeRangeType = 'Time Range Type is required.';
        }
        // Validate custom questions
        if (isCustomQuestion) {
            validationErrors = validateQuestionValues(questionValues) || {};
        }

        // Proceed only if no validation errors
        if (_.isEmpty(validationErrors)) {
            const res = await handleDuplicate(questionValues);

            if (res) {
                const { label, tableLabel } = questionValues;

                // Check for duplicates
                if (res.label?.trim() === label?.trim()) {
                    validationErrors.label = 'Question Label already taken, please use another label.';
                }
                if (res.tableLabel?.trim() === tableLabel?.trim()) {
                    validationErrors.tableLabel = 'Table Question Label already taken, please use another label.';
                }
            }
        }
        
        setErrors(validationErrors);

        if (Object.keys(validationErrors).length > 0) {
            setOpenErrorDialog(true); // Open the dialog if there are errors
            return;
        }
        if (editQuestion?._id) {
            if (editQuestion?.isArchived) {
                onSave(isCustomQuestion);
            } else {
                setIsArchivedDialog(true);
            }
        } else {
            setConfirmDialog(true);
        }
    };

    const handleAddOption = () => {
        setQuestionValues(prevValues => ({
            ...prevValues,
            customQuestionOptions: [...prevValues.customQuestionOptions, ''],
        }));
    };

    const handleRemoveOption = (index) => {
        setQuestionValues(prevValues => ({
            ...prevValues,
            customQuestionOptions: prevValues.customQuestionOptions.filter((_, i) => i !== index),
        }));
    };

    const handleEditOption = (index, value) => {
        setQuestionValues(prevValues => ({
            ...prevValues,
            customQuestionOptions: prevValues.customQuestionOptions.map((option, i) =>
                i === index ? value : option
            ),
        }));
    };

    const handleInputChange = (field, value) => {
        setQuestionValues(prevValues => ({
            ...prevValues,
            [field]: value,
        }));
    };


    const handleCloseErrorDialog = () => {
        setOpenErrorDialog(false);
    };

    return (
        <>
            <QuestionErrorList
                errors={errors}
                openErrorDialog={openErrorDialog}
                handleCloseErrorDialog={handleCloseErrorDialog}
            />
            {!!infoOpen &&
                <TemplateInfoDialog
                    onClose={() => setInfoOpen(false)}
                    open={infoOpen}
                    templateType={questionValues?.templateType}
                    handleSelectTemplate={(value) => handleInputChange('templateType', value)}
                />
            }
            {!!accountQuestions &&
                <AccountQuestions
                    handleClose={() => {
                        refreshQuestions();
                        handleClose();
                    }}
                    onClose={() => setAccountQuestions(false)}
                    latestType={latestType}
                    transferType={transferType}
                />
            }

            <Dialog open={open} onClose={handleClose} fullWidth maxWidth="md">
                <StyledDialogTitle>
                    {editQuestion?._id ? 'Edit Question' : 'Add Question'}
                    <Box>
                        <Button onClick={() => {
                            setAccountQuestions(true)
                        }}
                            variant='outlined' color='primary' sx={{ marginRight: 2 }}>
                            Add Question From Archive
                        </Button>
                        <IconButton aria-label="close" onClick={handleClose}>
                            <CloseIcon />
                        </IconButton>
                    </Box>
                </StyledDialogTitle>
                <DialogContent sx={{ padding: '20px' }}>
                    {!isCustomQuestion && (<>
                        <FormControl fullWidth margin="normal">
                            <TextField
                                label="Question Label"
                                variant="outlined"
                                fullWidth
                                value={questionValues.label}
                                onChange={(e) => handleInputChange('label', e.target.value)}
                            />
                        </FormControl>
                        <FormControl fullWidth margin="normal">
                            <TextField
                                label="Table Question Label"
                                variant="outlined"
                                fullWidth
                                value={questionValues.tableLabel}
                                onChange={(e) => handleInputChange('tableLabel', e.target.value)}
                            />
                        </FormControl>
                    </>
                    )}
                    {isCustomQuestion && (<>
                        <FormControl fullWidth margin="normal">
                            <InputLabel id="question-type-label-id">Question Type</InputLabel>
                            <Select
                                labelId="question-type-label-id"
                                id="question-type"
                                value={questionValues.customQuestionInputType}
                                label="Question Type"
                                onChange={(e) => {
                                    handleInputChange('customQuestionInputType', e.target.value)
                                    setQuestionValues(prevValues => ({
                                        ...prevValues,
                                        customQuestionOptions: [],
                                    }));
                                }}
                            >
                                {QUESTION_TYPE_OPTIONS.map((option) => (
                                    <MenuItem key={option.value} value={option.value}>
                                        {option.label}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                        <FormControl fullWidth margin="normal">
                            <TextField
                                label="Question Label"
                                variant="outlined"
                                fullWidth
                                value={questionValues.label}
                                onChange={(e) => handleInputChange('label', e.target.value)}
                            />
                        </FormControl>

                        {/* Auto-fill functionality */}
                        {supportsAutoFill(questionValues.customQuestionInputType) ? (
                            <Box sx={{ mt: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={enableAutoFill}
                                            onChange={(e) => handleAutoFillToggle(e.target.checked)}
                                            color="primary"
                                        />
                                    }
                                    label="Enable auto-fill from existing questions"
                                />

                                {enableAutoFill && (
                                    <Box sx={{ mt: 2 }}>
                                        <FormControl fullWidth margin="normal">
                                            <InputLabel id="source-transition-type-label">Source Transition Type</InputLabel>
                                            <Select
                                                labelId="source-transition-type-label"
                                                value={sourceTransitionType}
                                                label="Source Transition Type"
                                                onChange={(e) => handleTransitionTypeChange(e.target.value)}
                                            >
                                                {transitionTypeOptions.map((option) => (
                                                    <MenuItem key={option.value} value={option.value}>
                                                        {option.label}
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                        </FormControl>

                                        {sourceTransitionType && (
                                            <FormControl fullWidth margin="normal">
                                                <InputLabel id="source-question-label">Source Question</InputLabel>
                                                <Select
                                                    labelId="source-question-label"
                                                    value={sourceQuestionId}
                                                    label="Source Question"
                                                    onChange={(e) => handleSourceQuestionChange(e.target.value)}
                                                    disabled={loadingSourceQuestions}
                                                >
                                                    {loadingSourceQuestions ? (
                                                        <MenuItem disabled>
                                                            <CircularProgress size={20} sx={{ mr: 1 }} />
                                                            Loading questions...
                                                        </MenuItem>
                                                    ) : availableSourceQuestions.length === 0 ? (
                                                        <MenuItem disabled>
                                                            No compatible questions found
                                                        </MenuItem>
                                                    ) : (
                                                        availableSourceQuestions.map((question) => (
                                                            <MenuItem key={question._id} value={question._id}>
                                                                {question.label}
                                                                {question.customQuestionInputType && (
                                                                    <span style={{ color: '#666', fontSize: '0.8em', marginLeft: '8px' }}>
                                                                        ({QUESTION_TYPE_OPTIONS.find(opt => opt.value === question.customQuestionInputType)?.label})
                                                                    </span>
                                                                )}
                                                            </MenuItem>
                                                        ))
                                                    )}
                                                </Select>
                                            </FormControl>
                                        )}

                                        {sourceTransitionType && availableSourceQuestions.length === 0 && !loadingSourceQuestions && (
                                            <Box sx={{ mt: 1, p: 1, backgroundColor: '#fff3cd', borderRadius: 1 }}>
                                                <span style={{ color: '#856404', fontSize: '0.9em' }}>
                                                    ❌ No compatible questions found for auto-fill.
                                                    {questionValues.customQuestionInputType === QUESTION_INPUT_TYPE.NUMBER_RANGE &&
                                                        ' Number questions can only pull from other number questions.'}
                                                    {questionValues.customQuestionInputType === QUESTION_INPUT_TYPE.DATE &&
                                                        ' Date questions can only pull from other date questions.'}
                                                </span>
                                            </Box>
                                        )}
                                    </Box>
                                )}
                            </Box>
                        ) : (
                            // Show info message for question types that don't support auto-fill
                            questionValues.customQuestionInputType && (
                                <Box sx={{ mt: 2, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                                    <span style={{ color: '#666', fontSize: '0.9em' }}>
                                        ❌ Auto-fill is not available for this question type.
                                        Questions with fixed options (dropdown, yes/no, etc.) cannot use auto-fill functionality.
                                    </span>
                                </Box>
                            )
                        )}

                        {questionValues.customQuestionInputType !== QUESTION_INPUT_TYPE.DATE && (
                        <FormControl fullWidth margin="normal">
                            <TextField
                                label="Table Question Label"
                                variant="outlined"
                                fullWidth
                                value={questionValues.tableLabel}
                                onChange={(e) => handleInputChange('tableLabel', e.target.value)}
                            />
                        </FormControl>
                        )}

                        {(![QUESTION_INPUT_TYPE.RESIDENCE_LIST, QUESTION_INPUT_TYPE.DATE, QUESTION_INPUT_TYPE.TIME_TAB_RESIDENT_LIST, QUESTION_INPUT_TYPE.TIME_TAB_RANGE].includes(questionValues.customQuestionInputType)) && (
                            <FormControl fullWidth margin="normal">                                
                                <Box display="flex" alignItems="center">
                                    <Button
                                        onClick={() => setInfoOpen(true)}
                                        sx={{ ml: 1 }}
                                        variant="text"
                                        startIcon={<WebIcon />}
                                    >
                                        Choose Template ({QUESTIONS_TEMPLATES.find(template => template.value === questionValues.templateType)?.label})
                                    </Button>
                                </Box>
                            </FormControl>
                        )}

                        {questionValues.customQuestionInputType === QUESTION_INPUT_TYPE.LIMITED_ANSWERS && (
                            <LimitedAnswersOptions
                                options={questionValues?.customQuestionOptions}
                                onEdit={handleEditOption}
                                onRemove={handleRemoveOption}
                                onAdd={handleAddOption}
                            />
                        )}

                        {questionValues.customQuestionInputType === QUESTION_INPUT_TYPE.NUMBER_RANGE && (
                            <NumberRangeType
                                setQuestionValues={setQuestionValues}
                                questionValues={questionValues}
                            />
                        )}

                        {questionValues.customQuestionInputType === QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS && (
                            <NumberRangeWithUnlimitedAnswer
                                setQuestionValues={setQuestionValues}
                                questionValues={questionValues}
                            />
                        )}

                        {/* {questionValues.customQuestionInputType === QUESTION_INPUT_TYPE.TIME_TAB_RESIDENT_LIST && (
                            <ResidentListOnlyInfo />
                        )} */}

                        {questionValues.customQuestionInputType === QUESTION_INPUT_TYPE.TIME_TAB_RANGE && (
                            <TimeTabConfiguration
                                setQuestionValues={setQuestionValues}
                                questionValues={questionValues}
                            />
                        )}
                    </>)}
                </DialogContent>
                <DialogActions sx={{ padding: '20px' }}>
                    <Button onClick={handleClose} variant="outlined" color="primary">
                        Cancel
                    </Button>
                    <Button onClick={handleSave} variant="contained" color="primary" disabled={loading}>
                        {loading ? (
                            <>
                                <CircularProgress size={15} /> &nbsp; Saving...
                            </>
                        ) : (
                            'Save question'
                        )}
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

export default AddQuestionDialog;