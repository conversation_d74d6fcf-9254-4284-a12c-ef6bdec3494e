import { useMemo } from "react";

const useAdditionalData = (dynamicCards, initialCards = [], customTabs = []) => {
    return useMemo(() => {
        const hospitalCardsArr = [...initialCards];

        dynamicCards?.forEach((ele) => {
            hospitalCardsArr.push({
                value: ele?.accessor,
                label: ele?.tableLabel || ele?.label,
                tooltipContent: ele?.tableLabel || ele?.label,
                timeRangeType: ele?.timeRangeType,
            });
        });

        if(customTabs?.length > 0) {            
            customTabs.forEach((ele) => {
                hospitalCardsArr.push({
                    value: ele?.accessor,
                    label: ele?.title ?? ele?.label,
                    tooltipContent: ele?.description ?? ele?.label,
                    isCustomTab : true,
                    type : ele?.type,
                    userId: ele?.userId,
                    _id : ele?._id ? String(ele?._id) : null,
                    timeRangeType: ele?.timeRangeType,
                    shareCount: ele?.shareCount,
                    isUniversalAccess: ele?.isUniversalAccess,
                    isShared: ele?.isShared,
                    isCustomShare: ele?.isCustomShare,
                    newTitle: ele?.title ?? null,
                    shareId: ele?.shareId,
                    isHidden: ele?.isHidden,
                    dateAnalysisStartQuestion: ele?.dateAnalysisStartQuestion,
                    dateAnalysisEndQuestion: ele?.dateAnalysisEndQuestion,
                });
            });
        }
        return hospitalCardsArr;
    }, [customTabs, dynamicCards, initialCards]);
};

export default useAdditionalData;
