const mongoose = require("mongoose");
const { AUTH_ROLES } = require("../../types/auth.type");
const CustomTab = mongoose.model("customTab");
const CustomTabShare = mongoose.model("customTabShare");
const CustomTabNotification = mongoose.model("customTabNotification");
const User = mongoose.model("user");


// Share a custom tab with users
const shareCustomTab = async (req) => {
    const { customTabId, recipientIds, shareMessage, isUniversal } = req.body;
    const { accountid } = req.headers;
    const creatorUser = req.user;

    try {
        // Validate custom tab exists and belongs to creator
        const customTab = await CustomTab.findOne({
            _id: customTabId,
            userId: creatorUser._id,
            accountId: accountid
        });

        if (!customTab) {
            return { status: 404, message: "Custom tab not found or you don't have permission to share it" };
        }

        // Update universal access flag (always save this boolean field)
        if (isUniversal !== undefined) {
            await CustomTab.findByIdAndUpdate(customTabId, {
                isUniversal: isUniversal
            });
        }

        // Handle case where recipientIds is empty (only updating universal flag)
        if (!recipientIds || recipientIds.length === 0) {
            // Find all existing share records for this tab
            const existingShares = await CustomTabShare.find({
                customTabId,
                isActive: true
            });

            let removedCount = 0;
            if (existingShares.length > 0) {
                for (const share of existingShares) {
                    // Remove the share record
                    await CustomTabShare.deleteOne({ _id: share._id });

                    // Remove the TAB_SHARED notification
                    await CustomTabNotification.deleteMany({
                        customTabId,
                        recipientId: share.recipientId,
                        type: 'TAB_SHARED'
                    });

                    // Send unshare notification if user had accepted the share
                    if (share.status === 'ACCEPTED') {
                        await CustomTabNotification.create({
                            customTabId,
                            recipientId: share.recipientId,
                            senderId: creatorUser._id,
                            accountId: accountid,
                            type: 'TAB_UNSHARED',
                            title: 'Custom Dashboard Tab Unshared',
                            message: `${creatorUser.fullName} has unshared the custom dashboard tab "${customTab.title}" with you.`,
                            actionRequired: false,
                            metadata: {
                                tabTitle: customTab.title,
                                page: customTab.page
                            }
                        });
                    }

                    removedCount++;
                }
            }
            // Update share count (subtract removed shares)
            if (removedCount > 0) {
                await CustomTab.findByIdAndUpdate(customTabId, {
                    $inc: { shareCount: -removedCount }
                });
            }

            return {
                status: 200,
                data: {
                    sharedWith: 0,
                    alreadyShared: 0,
                    updatedToExplicit: 0,
                    removedShares: removedCount,
                    message: `Custom tab updated successfully.`,
                    results: []
                }
            };
        }

        // Validate recipients exist and belong to same account with access
        const allOtherUsers = await User.find({ _id: { $ne: creatorUser._id } })
            .populate('role')
            .select('fullName email accounts requirePasswordReset jobTitle role')
            .lean();

        const superAdminUsers = allOtherUsers.filter(u => u.role && u.role.slug === AUTH_ROLES.SUPER);

        const recipients = await User.find({
            _id: { $in: recipientIds, $ne: creatorUser._id }, // Can't share with yourself
            accounts: {
                $elemMatch: {
                    accountId: accountid,
                    access: true
                }
            }
        }).select("-facilities");

        // Separate superAdmins into explicitly selected and auto-included
        // - explicitlySelectedSuperAdmins: superAdmins that were selected in recipientIds (regular flow)
        // - autoIncludedSuperAdmins: superAdmins NOT selected in recipientIds (special records needed)
        const recipientIdStrings = recipientIds.map(id => id.toString());
        const explicitlySelectedSuperAdmins = superAdminUsers.filter(sa => recipientIdStrings.includes(sa._id.toString()));
        const autoIncludedSuperAdmins = superAdminUsers.filter(sa => !recipientIdStrings.includes(sa._id.toString()));

        // Add explicitly selected superAdmins to recipients (in case they weren't found by the DB query due to different account structure)
        if (explicitlySelectedSuperAdmins.length > 0) {
            recipients.push(...explicitlySelectedSuperAdmins);
        }

        // Add auto-included superAdmins to recipients (they will be marked as special records)
        if (autoIncludedSuperAdmins.length > 0) {
            recipients.push(...autoIncludedSuperAdmins);
        }
        const results = [];
        const notifications = [];

        // Remove duplicate recipients (by _id)
        const uniqueRecipientsMap = new Map();
        for (const user of recipients) {
            uniqueRecipientsMap.set(user._id.toString(), user);
        }
        const uniqueRecipients = Array.from(uniqueRecipientsMap.values());

        // Prepare a list of all recipient IDs and auto-included superAdmin IDs
        const superAdminIds = superAdminUsers.map(u => u._id.toString());
        const autoIncludedSuperAdminIds = autoIncludedSuperAdmins.map(u => u._id.toString());        

        // --- BEGIN UNSHARE LOGIC ---
        // Find all previous recipientIds for this customTabId
        const oldRecipientIds = await CustomTabShare.find({ customTabId }).distinct('recipientId');
        const newRecipientIds = uniqueRecipients.map(u => u._id.toString());
        const removedRecipientIds = oldRecipientIds.filter(id => !newRecipientIds.includes(id.toString()));

        for (const removedId of removedRecipientIds) {
            // Find the share record
            const shareRecord = await CustomTabShare.findOne({ customTabId, recipientId: removedId });

            // Remove the share record
            await CustomTabShare.deleteOne({ customTabId, recipientId: removedId });

            // Remove the TAB_SHARED notification
            await CustomTabNotification.deleteMany({
                customTabId,
                recipientId: removedId,
                type: 'TAB_SHARED'
            });

            // Only send 'unshared' notification if user had accepted the share
            if (shareRecord && shareRecord.status === 'ACCEPTED') {
                await CustomTabNotification.create({
                    customTabId,
                    recipientId: removedId,
                    senderId: creatorUser._id,
                    accountId: accountid,
                    type: 'TAB_UNSHARED',
                    title: 'Custom Dashboard Tab Unshared',
                    message: `${creatorUser.fullName} has unshared the custom dashboard tab "${customTab.title}" with you.`,
                    actionRequired: false,
                    metadata: {
                        tabTitle: customTab.title,
                        page: customTab.page
                    }
                });
            } else {
                console.log('[UNSHARE] Not sending unshare notification to:', removedId, 'shareRecord status:', shareRecord ? shareRecord.status : 'not found');
            }
        }
        // --- END UNSHARE LOGIC ---

        for (const recipient of uniqueRecipients) {
            // Check if already shared
            const existingShare = await CustomTabShare.findOne({
                customTabId,
                recipientId: recipient._id,
                isActive: true,
                status: { $in: ['PENDING', 'ACCEPTED', 'KEPT'] }
            });

            // Check if this recipient is an auto-included superAdmin
            const isAutoIncluded = autoIncludedSuperAdminIds.includes(recipient._id.toString());
            const isExplicitlySelected = recipientIdStrings.includes(recipient._id.toString());
            const isSuperAdmin = superAdminIds.includes(recipient._id.toString());

            if (existingShare) {
                // Special case: If superAdmin was previously auto-included but now explicitly selected,
                // update the record to reflect explicit selection
                if (existingShare.isAutoIncluded && isExplicitlySelected && superAdminIds.includes(recipient._id.toString())) {
                    existingShare.isAutoIncluded = false;
                    await existingShare.save();

                    results.push({
                        recipientId: recipient._id,
                        recipientName: `${recipient.firstName || recipient.fullName || ''} ${recipient.lastName || ''}`.trim(),
                        status: 'updated_to_explicit'
                    });
                } else {
                    results.push({
                        recipientId: recipient._id,
                        recipientName: `${recipient.firstName || recipient.fullName || ''} ${recipient.lastName || ''}`.trim(),
                        status: 'already_shared'
                    });
                }
                continue;
            }

            // Create share record
            const shareRecord = new CustomTabShare({
                customTabId,
                creatorId: creatorUser._id,
                recipientId: recipient._id,
                accountId: accountid,
                shareMessage,
                status: 'PENDING',
                isAutoIncluded: isAutoIncluded
            });

            await shareRecord.save();

            // Prepare notification metadata
            let notificationMetadata = {
                tabTitle: customTab.title,
                page: customTab.page,
                shareMessage,
                isAutoIncluded: isAutoIncluded
            };

            // If recipient is a superAdmin, add otherRecipients (excluding this superAdmin)
            if (superAdminIds.includes(recipient._id.toString())) {
                notificationMetadata.otherRecipients = uniqueRecipients
                    .filter(u => u._id.toString() !== recipient._id.toString())
                    .map(u => ({
                        _id: u._id,
                        fullName: `${u.firstName || u.fullName || ''} ${u.lastName || ''}`.trim()
                    }));
            }

            // Create notification
            const notification = new CustomTabNotification({
                customTabId,
                customTabShareId: shareRecord._id,
                recipientId: recipient._id,
                senderId: creatorUser._id,
                accountId: accountid,
                type: 'TAB_SHARED',
                title: 'Custom Dashboard Tab Shared',
                message: `${creatorUser.fullName} shared a custom dashboard tab "${customTab.title}" with you.`,
                actionRequired: true,
                metadata: notificationMetadata
            });

            notifications.push(notification);

            results.push({
                recipientId: recipient._id,
                recipientName: `${recipient.fullName || ''}`.trim(),
                status: 'shared',
                shareId: shareRecord._id
            });
        }

        // Bulk insert notifications
        if (notifications.length > 0) {
            await CustomTabNotification.insertMany(notifications);
        }

        // Update share count
        await CustomTab.findByIdAndUpdate(customTabId, {
            $inc: { shareCount: notifications.length }
        });

        // Calculate shared count excluding auto-included superAdmins
        let sharedCount = results.filter(r => r.status === 'shared').length;
        if (autoIncludedSuperAdmins.length > 0) {
            sharedCount -= autoIncludedSuperAdmins.length;
        }

        // Count updated records (superAdmins changed from auto-included to explicit)
        const updatedToExplicit = results.filter(r => r.status === 'updated_to_explicit').length;

        return {
            status: 200,
            data: {
                sharedWith: sharedCount + updatedToExplicit, // Include updated records in shared count
                alreadyShared: results.filter(r => r.status === 'already_shared').length,
                autoIncludedSuperAdmins: autoIncludedSuperAdmins.length,
                updatedToExplicit: updatedToExplicit,
                results
            }
        };

    } catch (error) {
        console.error("Error sharing custom tab:", error);
        return { status: 500, message: "Failed to share custom tab" };
    }
};

// Respond to a share invitation
const respondToShare = async (req) => {
    const { shareId, response, newTitle } = req.body; // response: 'accept', 'keep', 'delete', newTitle for conflicts
    const user = req.user;
    const { accountid } = req.headers;

    try {
        const shareRecord = await CustomTabShare.findOne({
            _id: shareId,
            recipientId: user._id,
            status: 'PENDING',
            isActive: true
        }).populate('customTabId').populate('creatorId');

        if (!shareRecord) {
            return { status: 404, message: "Share invitation not found" };
        }

        // For accept and keep responses, check for title conflicts
        if (response === 'accept' || response === 'keep') {
            const CustomTab = mongoose.model("customTab");
            const titleToCheck = newTitle || shareRecord.customTabId.title;

            const existingTab = await CustomTab.findOne({
                accountId: accountid,
                userId: user._id,
                title: new RegExp(`^${titleToCheck}$`, 'i'), // Case-insensitive check
                page: shareRecord.customTabId.page
            });

            if (existingTab) {
                return {
                    status: 409, // Conflict status
                    message: "Title conflict detected",
                    data: {
                        conflict: true,
                        originalTitle: shareRecord.customTabId.title,
                        conflictingTitle: existingTab.title,
                        shareId: shareId
                    }
                };
            }
        }

        let newStatus;
        let message;

        switch (response) {
            case 'accept':
                newStatus = 'ACCEPTED';
                shareRecord.viewedAt = new Date();
                message = `You accepted the shared tab "${shareRecord.customTabId.title}"`;
                break;
            case 'keep':
                newStatus = 'KEPT';
                message = `You kept the shared tab "${shareRecord.customTabId.title}" for later`;
                break;
            case 'delete':
                newStatus = 'DELETED';
                message = `You declined the shared tab "${shareRecord.customTabId.title}"`;
                break;
            default:
                return { status: 400, message: "Invalid response. Must be 'accept', 'keep', or 'delete'" };
        }

        shareRecord.status = newStatus;
        shareRecord.respondedAt = new Date();
        await shareRecord.save();

        // If a new title was provided (for conflict resolution), add it to the share record
        let customTabData = shareRecord.customTabId;
        if (newTitle && (response === 'accept' || response === 'keep')) {
            // Add the new title to the share record instead of creating a new tab
            shareRecord.newTitle = newTitle;
            await shareRecord.save();

            // Update the customTabData to show the new title in responses
            customTabData = {
                ...shareRecord.customTabId.toObject(),
                title: newTitle
            };
        }

        // Create response notification for creator
        const responseNotification = new CustomTabNotification({
            customTabId: shareRecord.customTabId._id,
            customTabShareId: shareRecord._id,
            recipientId: shareRecord.creatorId._id,
            senderId: user._id,
            accountId: shareRecord.accountId,
            type: 'SHARE_RESPONSE',
            title: 'Share Response',
            message: `${user.fullName} ${response === 'delete' ? 'declined' : response + 'ed'} your shared tab "${customTabData.title}"`,
            actionRequired: false,
            metadata: {
                response,
                tabTitle: customTabData.title,
                page: customTabData.page
            }
        });

        await responseNotification.save();

        return {
            status: 200,
            data: {
                response: newStatus,
                message,
                customTab: response !== 'delete' ? customTabData : null
            }
        };

    } catch (error) {
        console.error("Error responding to share:", error);
        return { status: 500, message: "Failed to respond to share" };
    }
};

// Unshare a tab from specific users
const unshareCustomTab = async (req) => {
    const { customTabId, recipientIds } = req.body;
    const user = req.user;

    try {
        // Validate custom tab belongs to user
        const customTab = await CustomTab.findOne({
            _id: customTabId,
            userId: user._id
        });

        if (!customTab) {
            return { status: 404, message: "Custom tab not found or you don't have permission" };
        }

        // Find active shares to unshare
        const sharesToUnshare = await CustomTabShare.find({
            customTabId,
            recipientId: { $in: recipientIds },
            isActive: true,
            status: { $in: ['PENDING', 'ACCEPTED', 'KEPT'] }
        }).populate('recipientId');

        if (sharesToUnshare.length === 0) {
            return { status: 404, message: "No active shares found for specified recipients" };
        }

        const notifications = [];
        const results = [];

        for (const shareRecord of sharesToUnshare) {
            // Update share status
            shareRecord.status = 'UNSHARED';
            shareRecord.isActive = false;
            await shareRecord.save();

            // Create unshare notification
            const notification = new CustomTabNotification({
                customTabId,
                customTabShareId: shareRecord._id,
                recipientId: shareRecord.recipientId._id,
                senderId: user._id,
                accountId: shareRecord.accountId,
                type: 'TAB_UNSHARED',
                title: 'Custom Tab Unshared',
                message: `${user.fullName} unshared the custom tab "${customTab.title}" from you`,
                actionRequired: false,
                metadata: {
                    tabTitle: customTab.title,
                    page: customTab.page
                }
            });

            notifications.push(notification);

            results.push({
                recipientId: shareRecord.recipientId._id,
                recipientName: `${shareRecord.recipientId.firstName} ${shareRecord.recipientId.lastName}`,
                status: 'unshared'
            });
        }

        // Bulk insert notifications
        if (notifications.length > 0) {
            await CustomTabNotification.insertMany(notifications);
        }

        // Update share count
        await CustomTab.findByIdAndUpdate(customTabId, {
            $inc: { shareCount: -results.length }
        });

        return {
            status: 200,
            data: {
                unsharedFrom: results.length,
                results
            }
        };

    } catch (error) {
        console.error("Error unsharing custom tab:", error);
        return { status: 500, message: "Failed to unshare custom tab" };
    }
};

// Get shared tabs for current user
const getSharedTabs = async (req) => {
    const user = req.user;
    const { accountid } = req.headers;
    const { status, showHidden } = req.query; // Optional filters

    try {
        const query = {
            recipientId: user._id,
            accountId: accountid,
            isActive: true
        };

        if (status) {
            query.status = status.toUpperCase();
        } else {
            query.status = { $in: ['PENDING', 'ACCEPTED', 'KEPT'] };
        }

        // Filter by hidden status
        if (showHidden === 'true') {
            query.isHidden = true; // Only show hidden tabs
        } else {
            query.isHidden = { $ne: true }; // Show non-hidden tabs (default)
        }

        const shares = await CustomTabShare.find(query)
            .populate({
                path: 'customTabId',
                select: 'title description page slug filters'
            })
            .populate({
                path: 'creatorId',
                select: 'firstName lastName email'
            })
            .select('+isAutoIncluded +newTitle +isHidden') // Include the isAutoIncluded, newTitle, and isHidden fields
            .sort({ createdAt: -1 });

        // Update the title in the response if newTitle exists
        const updatedShares = shares.map(share => {
            if (share.newTitle && share.customTabId) {
                return {
                    ...share.toObject(),
                    customTabId: {
                        ...share.customTabId.toObject(),
                        title: share.newTitle // Use the resolved title
                    }
                };
            }
            return share;
        });

        return {
            status: 200,
            data: updatedShares
        };

    } catch (error) {
        console.error("Error getting shared tabs:", error);
        return { status: 500, message: "Failed to get shared tabs" };
    }
};

// Get users who have access to a specific tab
const getTabShares = async (req) => {
    const { customTabId } = req.params;
    const user = req.user;

    try {
        // Validate tab belongs to user
        const customTab = await CustomTab.findOne({
            _id: customTabId,
            userId: user._id
        });

        if (!customTab) {
            return { status: 404, message: "Custom tab not found or you don't have permission" };
        }

        const shares = await CustomTabShare.find({
            customTabId,
            isActive: true,
            status: { $in: ['PENDING', 'ACCEPTED', 'KEPT'] }
        })
            .populate({
                path: 'recipientId',
                select: 'firstName lastName email'
            })
            .select('+isAutoIncluded') // Include the isAutoIncluded field
            .sort({ createdAt: -1 });

        return {
            status: 200,
            data: { shares, customTab }
        };

    } catch (error) {
        console.error("Error getting tab shares:", error);
        return { status: 500, message: "Failed to get tab shares" };
    }
};

// Get notifications for current user
const getNotifications = async (req) => {
    const user = req.user;
    const { accountid } = req.headers;
    const { limit = 20, skip = 0, unreadOnly = false, page = null } = req.query;

    try {
        const query = {
            recipientId: user._id,
            accountId: accountid
        };

        if (unreadOnly === 'true') {
            query.isRead = false;
        }

        // Find notifications and populate customTabId to filter by page
        let notificationsQuery = CustomTabNotification.find(query)
            .populate({
                path: 'customTabId',
                select: 'title page'
            })
            .populate({
                path: 'senderId',
                select: 'fullName jobTitle'
            })
            .sort({ createdAt: -1 });

        // Apply limit and skip
        if (limit) notificationsQuery = notificationsQuery.limit(parseInt(limit));
        if (skip) notificationsQuery = notificationsQuery.skip(parseInt(skip));

        const allNotifications = await notificationsQuery;

        // Filter by page if specified
        let notifications = allNotifications;
        if (page) {
            notifications = allNotifications.filter(notification => {
                // Check if customTabId exists and has page property
                if (notification.customTabId && notification.customTabId.page === page) {
                    return true;
                }
                // If customTabId is null (tab was deleted), check metadata for page
                if (!notification.customTabId && notification.metadata && notification.metadata.page === page) {
                    return true;
                }
                return false;
            });
        }

        // Count unread notifications (filter by page if specified)
        let unreadQuery = {
            recipientId: user._id,
            accountId: accountid,
            isRead: false
        };

        let unreadCount = 0;
        if (page) {
            // For page-specific unread count, we need to populate and filter
            const unreadNotifications = await CustomTabNotification.find(unreadQuery)
                .populate({
                    path: 'customTabId',
                    select: 'page'
                });
            unreadCount = unreadNotifications.filter(n => {
                // Check if customTabId exists and has page property
                if (n.customTabId && n.customTabId.page === page) {
                    return true;
                }
                // If customTabId is null (tab was deleted), check metadata for page
                if (!n.customTabId && n.metadata && n.metadata.page === page) {
                    return true;
                }
                return false;
            }).length;
        } else {
            unreadCount = await CustomTabNotification.countDocuments(unreadQuery);
        }

        return {
            status: 200,
            data: {
                notifications,
                unreadCount,
                hasMore: notifications.length === parseInt(limit)
            }
        };

    } catch (error) {
        console.error("Error getting notifications:", error);
        return { status: 500, message: "Failed to get notifications" };
    }
};

// Mark notification as read
const markNotificationRead = async (req) => {
    const { notificationId } = req.params;
    const user = req.user;

    try {
        const notification = await CustomTabNotification.findOneAndUpdate(
            {
                _id: notificationId,
                recipientId: user._id,
                isRead: false
            },
            {
                isRead: true,
                readAt: new Date()
            },
            { new: true }
        );

        if (!notification) {
            return { status: 404, message: "Notification not found" };
        }

        return {
            status: 200,
            data: notification
        };

    } catch (error) {
        console.error("Error marking notification as read:", error);
        return { status: 500, message: "Failed to mark notification as read" };
    }
};

// Check if user can access a custom tab (including shared tabs and superadmin access)
const canAccessCustomTab = async (userId, customTabId, accountId) => {
    try {
        // Check if user is the creator
        const customTab = await CustomTab.findOne({
            _id: customTabId,
            accountId: accountId
        });

        if (!customTab) {
            return { canAccess: false, canEdit: false, accessType: 'not_found' };
        }

        if (customTab.userId.toString() === userId.toString()) {
            return { canAccess: true, canEdit: true, accessType: 'creator' };
        }

        // Check if tab has universal access
        if (customTab.isUniversal) {
            // Verify user belongs to the same account
            const user = await User.findOne({
                _id: userId,
                accounts: {
                    $elemMatch: {
                        accountId: accountId,
                        access: true
                    }
                }
            });

            if (user) {
                return { canAccess: true, canEdit: false, accessType: 'universal' };
            }
        }

        // Check if tab is shared with user
        const sharedAccess = await CustomTabShare.findOne({
            customTabId,
            recipientId: userId,
            accountId: accountId,
            isActive: true,
            status: { $in: ['ACCEPTED', 'KEPT'] }
        });

        if (sharedAccess) {
            return { canAccess: true, canEdit: false, accessType: 'shared' };
        }

        // Check if user is superadmin or has total permissions
        const user = await User.findById(userId).populate('role');
        const userRole = user?.role?.slug;

        if (userRole === AUTH_ROLES.ADMIN || userRole === AUTH_ROLES.SUPER) {
            return { canAccess: true, canEdit: false, accessType: 'admin' };
        }

        return { canAccess: false, canEdit: false, accessType: 'none' };

    } catch (error) {
        console.error("Error checking tab access:", error);
        return { canAccess: false, canEdit: false, accessType: 'error' };
    }
};

module.exports = {
    shareCustomTab,
    respondToShare,
    unshareCustomTab,
    getSharedTabs,
    getTabShares,
    getNotifications,
    markNotificationRead,
    canAccessCustomTab
}; 